* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html, h1, h2, h3, h4, h5, h6, p, a, span, div, ul, ol, li, input, button, textarea, select, label, table, th, td, tr, form, nav, header, footer, section, article, aside, main {
    font-family: 'Gotham Narrow', Arial, sans-serif !important;
}

body {
    line-height: 1.6;
    color: #333;
    margin: 0;
    height: 100%
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: white;
    padding: 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    font-size: 18px;
    font-weight: bold;
    color: #102649;
}

.nav-links {
    display: none;
    flex-wrap: wrap;
    gap: 20px;
}

.nav-links a {
    text-decoration: none;
    color: #595959;
    font-size: 14px;
    font-weight: 325;
}

.nav-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-login {
    background: #007DB6;
    color: white;
}

.btn-register {
    background: #F7C100;
    color: #102649;
}

.mobile-toggle {
    display: block;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
}

/* Hero Header Content */
.hero-header {
    position: relative;
    z-index: 10;
    background: rgba(16, 38, 73, 0.9);
    padding: 20px 0;
}

.hero-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.hero-title {
    /* font-size: clamp(20px, 4vw, 36px); */
    font-weight: 400;
    text-transform: uppercase;
    line-height: 1.2;
    margin: 0;
    color: white;
    flex: 1;
    min-width: 250px;
}

.hero-countdown {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.hero-countdown-item {
    text-align: center;
    color: white;
    min-width: 60px;
}

.hero-countdown-number {
    /* font-size: 28px; */
    font-weight: 400;
    line-height: 1;
    display: block;
    animation: pulse 2s infinite;
}

.hero-countdown-label {
    /* font-size: 11px; */
    font-weight: 400;
    line-height: 1;
    margin-top: 5px;
    display: block;
}

/* Hero Section with Video */
:root {
    /* Heights used in hero full-screen calculation */
    --top-header-height: 72px; /* blue event header bar */
    --bottom-nav-height: 50px; /* dark nav bar under video */
}

.hero {
    position: relative;
    height: calc(100vh - var(--top-header-height) - var(--bottom-nav-height));
    width: 100%;
    overflow: hidden;
}

/* Make the iframe cover the section with proper responsive behavior */
.hero-video-iframe {
    position: absolute;
    top: 50%;
    left: 50%;
    /* make sure it’s always in the back but above the fallback bg */
    z-index: 1;
    border: none;
    pointer-events: none; /* keep it non-interactive */
    transform: translate(-50%, -50%);

    /* Responsive sizing that maintains aspect ratio without zooming */
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;

    /* Ensure video covers container properly without distortion */
    object-fit: cover;
    object-position: center;

    /* Prevent browser scaling issues */
    max-width: none;
    max-height: none;
}


/* Fallback background when video fails */
.hero-fallback-bg {
    background-size: cover;
    background: linear-gradient(135deg, #102649 0%, #007DB6 50%, #F7C100 100%) center;
    z-index: 0;
}

/* Show fallback when video has errors */
.hero-video-error .hero-fallback-bg {
    display: block !important;
}

.hero-video-error .hero-video-iframe {
    display: none;
}

/* Optional dark/gradient overlay */
.hero-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, .35); /* tweak/replace as needed   */
    pointer-events: none; /* keep the video clickable */
    z-index: 2;
}

/* Navigation Bar */
.nav-bar {
    background: #102649;
    padding: 6px 0;
}

.nav-bar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 50px; /* allow expansion */
    height: auto;
    flex-wrap: wrap; /* permits rows when collapse expands */
}

.nav-bar-links a {
    color: #fff;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Gotham Narrow', sans-serif;
    margin-right: 24px;
    text-decoration: none;
}

.nav-bar-actions .btn {
    padding: 6px 22px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px;
}

.nav-bar-actions .btn-login {
    background: #007db6;
    color: #fff;
}

.nav-bar-actions .btn-register {
    background: #f7c100;
    color: #11294d;
}

.logout-link {
    color: #fff;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    font-weight: 600;
}

/* CSS Variables for navbar heights */
:root {
    --top-header-height: 73px;
    --bottom-nav-height: 62px;
}

/* Top navbar responsive styling */
.navbar-toggler {
    border: 1px solid #dee2e6;
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Ensure proper spacing for top navbar */
.navbar .container {
    position: relative;
}

.navbar-collapse {
    z-index: 1000;
}

/* Bottom navbar responsive styling */
.nav-bar.navbar {
    padding: 6px 0;
    background: #102649;
}

.nav-bar .navbar-brand {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
}

.nav-bar .navbar-brand:hover {
    color: #f7c100;
}

.nav-bar .navbar-toggler {
    border: 1px solid #fff;
    padding: 4px 8px;
    background: transparent;
}

.nav-bar .navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.nav-bar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

.nav-bar .nav-link {
    color: #fff !important;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Gotham Narrow', sans-serif;
    text-decoration: none;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.nav-bar .nav-link:hover {
    color: #f7c100 !important;
}

/* Button styling for bottom navbar */
.nav-bar .btn-primary {
    background: #007db6;
    border-color: #007db6;
    color: #fff;
    padding: 6px 22px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
}

.nav-bar .btn-warning {
    background: #f7c100;
    border-color: #f7c100;
    color: #11294d;
    padding: 6px 22px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
}

.nav-bar .btn-outline-light {
    color: #fff;
    border-color: #fff;
    padding: 6px 22px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 400;
}

/* Unified Section Headers */
.section-header,
.tickets-header-section,
.venue-header,
.gallery-header,
.sponsors-header-section,
.contact-header {
    background: #102649;
    height: 73px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.section-header,
.tickets-header-title,
.venue-title,
.gallery-title,
.sponsors-header-section .section-header {
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    padding-left: 38px;
    margin: 0;
}

.venue-header-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Remove redundant padding/margins that conflict */
.tickets-header-section {
    padding: 0;
}

/* Event Details */
.event-details {
    padding: 40px 0;
    background: white;
}

.event-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    align-items: center;
    margin-top: 20px;
}

.event-image {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures the image fills the container, cropping if necessary */
    border-radius: 8px; /* Ensures the border radius also applies to the image */
}

.event-text h2 {
    color: #102649;
    font-size: 24px;
    font-weight: 400;
    text-transform: capitalize;
    margin-bottom: 10px;
}

.event-text p {
    color: #102649;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.75;
    margin-bottom: 10px;
}

/* Event Features */
.event-features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 10px;
    margin-top: 10px;
    grid-auto-rows: auto;
}

.feature-card {
    border: 2px solid #102649;
    padding: 15px;
    text-align: center;
    background: white;
}

.feature-card.yellow {
    border-color: #F7C100;
}

.feature-card.blue {
    border-color: #007DB6;
}

.feature-title {
    color: #102649;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.feature-description {
    color: #102649;
    font-size: 10px;
    font-weight: 325;
    line-height: 1.4;
}

.centered-features {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: nowrap;
}

.centered-features .feature-card {
    flex: 0 1 260px;
    max-width: 280px;
}

/* Stats */
.stats {

    text-align: center;
    background: #102649;
    padding: 30px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.stats > .container {
    max-width: 1200px;

    padding: 0 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    text-align: center;
}

.stat-item {
    color: white;
}

.stat-number {
    font-size: 28px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
}

@media (max-width: 768px) {
    .stats {
        margin-left: 0;
        padding: 25px 0;
    }

    .stats > .container {
        padding: 0 15px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .stat-number {
        font-size: 24px;
    }

    .stat-label {
        font-size: 11px;
        line-height: 18px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Benefits */
.benefits {
    padding: 40px 0;
}

.benefits-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 20px;
}

.benefit-card {
    display: flex;
    background: #102649;
    border-radius: 0px;
    overflow: hidden;
    min-height: 150px;
}

.benefit-card.yellow {
    background: #F7C100;
}

.benefit-card.blue {
    background: #007DB6;
}

.benefit-image {
    width: 40%;
    background: #f0f0f0;
    position: relative;
}

.benefit-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.benefit-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.benefit-title {
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 12px;
}

.benefit-card .benefit-title {
    color: white;
}

.benefit-card.yellow .benefit-title {
    color: #102649;
}

.benefit-description {
    font-size: 16px;
    font-weight: 325;
    line-height: 1.5;
}

.benefit-card .benefit-description {
    color: white;
}

.benefit-card.yellow .benefit-description {
    color: #102649;
}

@media (max-width: 768px) {
    .benefit-card {
        flex-direction: column;
    }

    .benefit-image {
        width: 100%;
        height: 200px;
    }

    .benefit-content {
        width: 100%;
        padding: 20px;
    }
}

/* Speakers */
.speakers {
    padding: 40px 0;
    background: white;
}

.speakers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.speaker-card {
    text-align: center;
}

.speaker-image {

    width: 120px;
    height: 120px;
    background: #102649;
    border-radius: 50%;
    margin: 0 auto 15px;
    border: 3px solid #F7C100;
}

/* New wrapper for organic masked speaker images */
.speaker-image-wrapper {
    width: 152px;
    height: 152px;
    margin: 0 auto 15px;
}

.speaker-image-wrapper svg {
    width: 100%;
    height: 100%;
    display: block;
}

/* ---- Alternative circular speaker photo with gradient ring ---- */
.speaker-photo {
    position: relative;
    width: 162.68px;
    height: 162.68px;
    margin: 15px auto 15px;
    border-radius: 50%;
    overflow: hidden;
    background: #102649; /* deep blue fill */
    transform: rotate(-43deg);
}

/* gradient outline using pseudo element */
.speaker-photo::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 178px; /* inner white ring diameter */
    height: 178px;
    border-radius: 50%;
    transform: translate(-50%, -50%) rotate(89deg);
    pointer-events: none;
    border: 3px solid #ffffff; /* white ring */
}

/* outer gradient ring */
.speaker-photo::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 212px; /* outer gradient ring diameter */
    height: 212px;
    border-radius: 50%;
    transform: translate(-50%, -50%) rotate(89deg);
    pointer-events: none;
    padding: 3px; /* ring thickness */
    background: linear-gradient(125deg, #D39429 0%, #FFF9C2 56%, #D29D6B 81%, #B05526 100%) border-box;
    /* cut out the middle to form only ring */
    -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #000 0);
    mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #000 0);
}

.speaker-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center 1%; /* shift image 10px downward approx */
    transform: translateY(5px) rotate(43deg); /* offset then counter-rotate to keep image upright */
}

.speaker-photo svg {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 152px;
    height: 152px;
    transform: translate(-50%, -50%) rotate(89deg);
    pointer-events: none;
}

.speaker-photo svg image {
    display: none;
}

/* text container under the photo */
.speaker-meta {
    width: 230px;
    margin: 0 auto;
    text-align: center;
}

.speaker-name {
    color: #102649;
    font: 400 16px/24px "Gotham Narrow", sans-serif;
    text-transform: uppercase;
}

.speaker-details {
    color: #007DB6;
    font: 350 12px/16px "Gotham Narrow", sans-serif;
    text-transform: capitalize;
}

.speaker-name {
    color: #102649;
    font-size: 16px;
    font-weight: 400;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.speaker-title {
    color: #007DB6;
    font-size: 12px;
    font-weight: 350;
    line-height: 1.3;
}

.speaker-company {
    color: #007DB6;
    font-size: 12px;
    font-weight: 400;
}

/* Contact Section */
.contact-section {
    background: #ffffff;
    padding-bottom: 60px;
}

.contact-header {
    background: #102649;
    height: 73px;
    display: flex;
    align-items: center;
    padding-left: 38px;
    overflow: hidden;
}

.contact-header h2 {
    color: #ffffff;
    font: 400 24px/48px "Gotham Narrow", sans-serif;
    text-transform: uppercase;
    margin: 0;
}

.contact-subtitle {
    color: #102649;
    font: 400 24px/48px "Gotham Narrow", sans-serif;
    margin: 20px 38px 0;
}

.contact-info {
    display: flex;
    gap: 64px;
    margin: 20px 38px 0;
    align-items: flex-start;
    flex-wrap: wrap;
}

.contact-block {
    max-width: 321px;
}

.contact-block-title {
    color: #007DB6;
    font: 400 24px/48px "Gotham Narrow", sans-serif;
    margin: 0 0 7px;
    text-align: center;
    text-transform: uppercase;
}

.contact-block-text {
    color: #102649;
    font: 350 16px/24px "Gotham Narrow", sans-serif;
}

.contact-block-text a {
    color: #102649;
    text-decoration: underline;
}

.contact-divider {
    width: 1px;
    height: 139px;
    background: rgba(16, 38, 73, 0.5);
}

/* Responsive Contact Section */
@media (max-width: 992px) {
    .contact-info {
        flex-direction: column;
        align-items: center;
        gap: 32px;
    }

    .contact-divider {
        display: none;
    }
}

@media (max-width: 768px) {
    .contact-header {
        padding-left: 20px;
        height: auto;
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .contact-header h2 {
        font-size: 22px;
        line-height: 1.4;
    }

    .contact-subtitle {
        margin-left: 20px;
        margin-right: 20px;
        font-size: 18px;
        text-align: center;
        line-height: 1.5;
    }

    .contact-info {
        margin-left: 20px;
        margin-right: 20px;
    }

    .contact-block {
        text-align: center;
        max-width: 100%;
    }
}

/* Tickets */
.tickets {
    padding: 0;
}

.tickets > .container {
    max-width: 1200px;
    padding: 0 20px;
}

.tickets-header-section {
    display: none;
}

.tickets-content {
    padding: 60px 20px;
}

.tickets-header {
    text-align: center;
    margin-bottom: 50px;
}

.tickets-title {
    color: #1e88e5;
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tickets-subtitle {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 500;
    text-transform: capitalize;
}

.tickets-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.ticket-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ticket-card:hover {
    transform: translateY(-5px);
    box-shadow: 0px 8px 30px rgba(0, 0, 0, 0.12);
}

.ticket-header {
    padding: 40px 30px 30px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
}

.ticket-type {
    color: #1e88e5;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ticket-price {
    color: #2c3e50;
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1;
}

.ticket-original-price {
    color: #999;
    font-size: 18px;
    font-weight: 400;
    text-decoration: line-through;
}

.ticket-features {
    padding: 30px;
    flex-grow: 1;
}

.ticket-feature {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 18px;
    font-size: 16px;
    color: #555;
    font-weight: 400;
}

.ticket-feature:last-child {
    margin-bottom: 0;
}

.ticket-feature-icon {
    width: 24px;
    height: 24px;
    background: #1e88e5;
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
}

.ticket-feature-icon::after {
    content: '✓';
    color: white;
    font-size: 14px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ticket-button {
    padding: 30px;
}

.ticket-btn {
    width: 100%;
    padding: 18px 50px;
    background: #102649;
    text-decoration: none;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    transition: background 0.3s ease;
}

.ticket-btn:hover {
    background: #34495e;
    transform: translateY(-2px);
}

/* CTA Section */
.cta {
    padding: 0 0 20px; /* Reduced bottom padding from 40px to 20px */
}

.cta .container {
    max-width: 1155px;
    margin: 0 auto;
    padding: 0 20px;
}

.cta .section-header {
    background: #102649;
    height: 44px;
    display: flex;
    align-items: center;
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    padding-left: 38px;
    margin: 0 0 30px;
}

.cta-content {
    background: #0779B5;
    padding: 40px;
    text-align: center;
    border-radius: 0px;
}

.cta-title {
    color: #F7C100;
    font-size: 20px;
    font-weight: 400;
    text-transform: capitalize;
    margin-bottom: 15px;
}

.cta-description {
    color: white;
    font-size: 16px;
    font-weight: 350;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background: #102649;
    color: white;
    padding: 12px 40px;
    border: none;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 400;
    text-transform: uppercase;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

@media (max-width: 768px) {
    .cta {
        padding: 0 0 10px; /* Even less padding on mobile */
    }

    .cta .section-header {
        font-size: 20px;
        padding-left: 20px;
        height: 60px;
    }

    .cta-content {
        padding: 30px 20px;
    }

    .cta-title {
        font-size: 18px;
    }

    .cta-description {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .cta-button {
        padding: 10px 30px;
        font-size: 16px;
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .cta-content {
        padding: 25px 15px;
    }

    .cta-title {
        font-size: 16px;
    }

    .cta-button {
        padding: 8px 20px;
        font-size: 14px;
    }
}

/* Footer */
.footer {
    background: #102649;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 15px;
}

.footer-section a {
    color: #F8F9FA;
    text-decoration: none;
    font-size: 14px;
    font-weight: 700;
    display: block;
    margin-bottom: 8px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.25);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.copyright {
    font-size: 14px;
    font-weight: 700;
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-links a {
    width: 16px;
    height: 16px;
    background: white;
    display: block;
}

/* Responsive Design */
@media (min-width: 768px) {
    .nav-links {
        display: flex;
    }

    .mobile-toggle {
        display: none;
    }

    .hero {
        max-height: 200px;
        min-height: 176px;
        object-fit: cover;
    }

    .hero-header {
        padding: 25px 0;
    }

    .hero-countdown {
        gap: 30px;
    }

    .tickets-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .event-content {
        grid-template-columns: 1fr 1fr;
    }

    .event-features {
        grid-template-columns: repeat(2, 1fr);
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 40px;
    }

    .hero {
        /* let the hero be as tall as the full-screen calculation again */
        height: calc(100vh - var(--top-header-height) - var(--bottom-nav-height));
        max-height: none; /* <-- undo the 200 px cap                */
        min-height: unset; /* optional – keeps CSS-lint happy        */
    }

    .hero-header {
        padding: 1.5rem 0;
    }

    .hero-countdown {
        gap: 40px;
    }

    .tickets-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .event-features {
        grid-template-columns: repeat(4, 1fr);
    }

    .nav-bar-links {
        justify-content: center;
    }

    .nav-bar {
        overflow-x: visible;
    }
}

/* Sponsors Section */
.sponsors-section,
section.event-venue-section,
section.contact-section {
    padding: 80px 0;
}

.sponsors-section > .container,
.event-venue-section > .container,
.contact-section > .container {
    max-width: 1200px; /* same as speakers */
    padding: 0 20px;
}

.sponsors-header-section {
    background: #102649;
    padding: 20px 0;
    margin-bottom: 40px;
}

.sponsors-header-section .section-header {
    color: white;
    text-align: center;
    margin: 0;
    height: 73px;
    line-height: 73px; /* vertically center text if not flex */
}

.sponsors-content-wrapper .container {
    max-width: 1200px;
}

.sponsors-content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.sponsors-section-title {
    color: #007DB6;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 30px;
}

/* Main Sponsors */
.main-sponsors-section {
    text-align: center;
    margin-bottom: 60px;
}

.main-sponsor-img {
    max-width: 459px;
    height: auto;
    margin: 0 auto;
}

/* Platinum Sponsors */
.platinum-sponsors-section {
    text-align: center;
    margin-bottom: 60px;
}

.platinum-sponsors-images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.platinum-sponsors-images img {
    height: auto;
}

/* Gold and Silver Container */
.gold-silver-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
}

/* Gold Sponsors */
.gold-sponsors-section {
    text-align: center;
}

.gold-sponsors-images {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Silver Sponsors */
.silver-sponsors-section {
    text-align: center;
}

.silver-sponsors-images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    justify-items: center;
    align-items: center;
}

.silver-sponsors-images img {
    max-width: 100%;
    height: auto;
}

/* Bottom Sponsors */
.bottom-sponsors {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.bottom-sponsors img {
    height: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gold-silver-container {
        grid-template-columns: 1fr;
    }

    .silver-sponsors-images {
        grid-template-columns: repeat(2, 1fr);
    }

    .platinum-sponsors-images,
    .bottom-sponsors {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .silver-sponsors-images {
        grid-template-columns: 1fr;
    }
}

/* Animation for countdown */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Event Venue Section */
.venue-header-container {
    max-width: 1200px;
    margin: 0 auto;
}

.event-venue-section {
    background: white;
    padding: 0 0 40px;
}

.event-venue-section .container {
    max-width: 1068px;
    margin: 0 auto;
    padding: 0 20px;
}

.event-venue-section .section-header {
    background: #102649;
    height: 73px;
    display: flex;
    align-items: center;
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    padding-left: 38px;
    margin: 0 0 30px;
}

.venue-content {
    width: 100%;
}

.venue-container {
    max-width: 1068px;
    margin: 0 auto;
    padding: 0;
    display: flex;
    align-items: stretch; /* Ensures children stretch to full height */
    gap: 0;
}

.venue-image-left,
.venue-image-right {
    flex: 1; /* Each column takes up equal space */
}

.venue-image-left,
.venue-image-right {
    flex: 1;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    height: 392px; /* Updated height as requested */
}

.venue-image-left img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.venue-image-left::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(20, 20, 20, 0.45);
    z-index: 1;
}

.venue-image-left .venue-text-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 40px;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.35);
}

@media (max-width: 600px) {
    .venue-image-left .venue-text-content {
        padding: 18px;
    }
}

.venue-image-right img,
.venue-image-right iframe {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border: 0;
}

.venue-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    height: 50%;
}

.venue-text-content {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 20px;
    color: white;
}

.venue-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.venue-description {
    font-size: 16px;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .event-venue-section {
        padding: 0 0 30px;
    }

    .event-venue-section .section-header {
        font-size: 20px;
        padding-left: 20px;
        height: 60px;
    }

    .venue-name {
        font-size: 20px;
    }

    .venue-description {
        font-size: 14px;
    }
}

@media (max-width: 600px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on mobile */
    }
}

@media (max-width: 480px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on smaller phones */
    }
}

.date-info {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.schedule-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.time-filter {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
    max-height: 100px;
    overflow-y: auto;
}

.filter-btn {
    background-color: var(--card-bg);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    white-space: nowrap;
}

.filter-btn:hover, .filter-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.time-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.time-btn:hover {
    background-color: var(--primary-color);
}

.schedule-table th:first-child {
    position: sticky;
    left: 0;
    z-index: 20;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    table-layout: fixed;
}

.schedule-table th {
    background-color: var(--secondary-color);
    color: #00244A;
    text-align: center;
    padding: 8px 4px;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.75rem;
    white-space: nowrap;
}

.schedule-table td {
    border: 1px solid #ddd;
    padding: 4px;
    vertical-align: top;
    height: 60px;
}

.stage-name {
    font-weight: bold;
    text-align: center;
    width: 120px;
    min-width: 120px;
    writing-mode: horizontal-tb;
    font-size: 0.85rem;
    /* Sticky positioning handled by td:first-child rule above */
}

.time-cell {
    width: 70px;
    min-width: 70px;
}

.event {
    border-radius: 4px;
    padding: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.75rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.event:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.event-title {
    font-weight: bold;
    margin-bottom: 2px;
    font-size: 0.8rem;
    line-height: 1.2;
}

.event-time {
    font-size: 0.7rem;
    color: #666;
    font-weight: 600;
}

.event-presenter {
    font-style: italic;
    font-size: 0.65rem;
    color: #666;
    margin-top: 2px;
}

.stage-1-event {
    background-color: #002b4a33;
    border-left: 4px solid #002b4a;
}

.stage-2-event {
    background-color: #007db633;
    border-left: 4px solid #007db6;
}

.stage-3-event {
    background-color: #77cbd533;
    border-left: 4px solid #77cbd5;
}

.stage-4-event {
    background-color: #ffcb1633;
    border-left: 4px solid #ffcb16;
}

.stage-5-event {
    background-color: #9f237833;
    border-left: 4px solid #9f2378;
}

.stage-6-event {
    background-color: #f7921e33;
    border-left: 4px solid #f7921e;
}

.stage-7-event {
    background-color: #e54b2133;
    border-left: 4px solid #e54b21;
}

.stage-8-event {
    background-color: #127e5e33;
    border-left: 4px solid #127e5e;
}

.stage-1-header {
    background-color: #002b4a !important;
    color: white;
}

.stage-2-header {
    background-color: #007db6 !important;
    color: white;
}

.stage-3-header {
    background-color: #77cbd5 !important;
    color: #002b4a;
}

.stage-4-header {
    background-color: #ffcb16 !important;
    color: #333;
}

.stage-5-header {
    background-color: #9f2378 !important;
    color: white;
}

.stage-6-header {
    background-color: #f7921e !important;
    color: white;
}

.stage-7-header {
    background-color: #e54b21 !important;
    color: white;
}

.stage-8-header {
    background-color: #127e5e !important;
    color: white;
}

.legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    padding: 20px 0;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #00244A;
}

.legend-color {
    width: 20px;
    height: 20px;
    margin-right: 5px;
    border-radius: 4px;
}

.schedule-container {
    overflow-x: auto;
    margin-bottom: 30px;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 100;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.close-modal {
    float: right;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
}

.modal-title {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.modal-details p {
    margin-bottom: 10px;
}

.break-event {
    background-color: #f8f9fa !important;
    text-align: center;
    border-left: 4px solid #6c757d !important;
    font-weight: bold;
}

.speaker-info {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.speaker-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 8px;
    object-fit: cover;
}

.speaker-details {
    font-size: 0.6rem;
}

.mobile-schedule {
    display: none;
}

/* Enhanced Mobile Styles */
@media (max-width: 768px) {
    .desktop-schedule {
        display: none;
    }

    .mobile-schedule {
        display: block;
        padding: 0 10px;
    }

    /* Mobile Navigation */
    .mobile-nav {
        position: sticky;
        top: 0;
        background: white;
        z-index: 20;
        padding: 15px 0;
        border-bottom: 2px solid #f0f0f0;
        margin-bottom: 20px;
    }

    .mobile-view-toggle {
        display: flex;
        background-color: #f8f9fa;
        border-radius: 25px;
        padding: 4px;
        margin-bottom: 15px;
    }

    .mobile-view-btn {
        flex: 1;
        background: none;
        border: none;
        padding: 10px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #666;
    }

    .mobile-view-btn.active {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .mobile-stage-filter {
        display: flex;
        gap: 8px;
        overflow-x: auto;
        padding-bottom: 10px;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .mobile-stage-filter::-webkit-scrollbar {
        display: none;
    }

    .mobile-stage-btn {
        background-color: white;
        border: 2px solid #ddd;
        color: #666;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 0.8rem;
        white-space: nowrap;
        min-width: fit-content;
    }

    .mobile-stage-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* Timeline View */
    .mobile-timeline-view {
        display: block;
    }

    .mobile-timeline-view.hidden {
        display: none;
    }

    .timeline-slot {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .timeline-time {
        font-size: 1.1rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 12px;
        display: flex;
        align-items: center;
    }

    .timeline-time::before {
        content: "";
        width: 12px;
        height: 12px;
        background-color: var(--primary-color);
        border-radius: 50%;
        margin-right: 10px;
    }

    .timeline-events {
        margin-left: 22px;
        position: relative;
    }

    .timeline-events::before {
        content: "";
        position: absolute;
        left: -16px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e0e0e0;
    }

    .timeline-event {
        background: white;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .timeline-event:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .timeline-event::before {
        content: "";
        position: absolute;
        left: -22px;
        top: 50%;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid var(--primary-color);
        transform: translateY(-50%);
    }

    .timeline-event-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
    }

    .timeline-event-title {
        font-weight: bold;
        font-size: 1rem;
        color: #333;
        line-height: 1.3;
        flex: 1;
        margin-right: 10px;
    }

    .timeline-event-duration {
        background-color: #f8f9fa;
        color: #666;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        white-space: nowrap;
    }

    .timeline-event-stage {
        background-color: var(--primary-color);
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 8px;
    }

    .timeline-event-speakers {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
    }

    .timeline-speaker {
        display: flex;
        align-items: center;
        background-color: #f8f9fa;
        padding: 6px 10px;
        border-radius: 20px;
    }

    .timeline-speaker-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 6px;
        object-fit: cover;
    }

    /* Stage View */
    .mobile-stage-view {
        display: none;
    }

    .mobile-stage-view.active {
        display: block;
    }

    .mobile-stage-section {
        margin-bottom: 25px;
    }

    .mobile-stage-section.hidden {
        display: none;
    }

    .mobile-stage-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        margin-bottom: 15px;
        font-size: 1.1rem;
        font-weight: bold;
        text-align: center;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .mobile-event {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border-left: 4px solid;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-event:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .mobile-event-time {
        background-color: #f8f9fa;
        color: var(--primary-color);
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 12px;
    }

    .mobile-event-title {
        font-size: 1.1rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    .mobile-event-description {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .speaker-info {
        display: flex;
        align-items: center;
        margin-top: 10px;
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-radius: 8px;
    }

    .speaker-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        object-fit: cover;
        border: 2px solid white;
    }

    .speaker-details {
        font-size: 0.85rem;
        line-height: 1.3;
    }

    .speaker-details strong {
        color: var(--primary-color);
        font-weight: 600;
    }

    /* Removed status indicators - keeping clean design */
    /* Empty State */
    .no-events {
        text-align: center;
        padding: 40px 20px;
        color: #999;
    }

    .no-events-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    /* Quick Actions */
    .mobile-quick-actions {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 30;
    }

    .quick-action-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        font-size: 1.2rem;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        margin-bottom: 10px;
        display: block;
    }

    .quick-action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    /* Enhanced Legend for Mobile */
    @media (max-width: 768px) {
        .legend {
            display: none;
        }

        .mobile-legend {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .mobile-legend-item {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
            padding: 8px;
            background: white;
            border-radius: 8px;
        }

        .mobile-legend-color {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border-radius: 4px;
        }
    }
}

/* Agenda Section */
.agenda-section {
    background-color: #ffffff;
    display: block;
    margin-top: 30px;
}

/* Legend Colors */
.legend-color.exhibit, .mobile-legend-color.exhibit {
    background-color: #002b4a;
}

.legend-color.main, .mobile-legend-color.main {
    background-color: #007db6;
}

.legend-color.game, .mobile-legend-color.game {
    background-color: #77cbd5;
}

.legend-color.talent, .mobile-legend-color.talent {
    background-color: #ffcb16;
}

.legend-color.orlov, .mobile-legend-color.orlov {
    background-color: #9f2378;
}

.legend-color.hope, .mobile-legend-color.hope {
    background-color: #f7921e;
}

.legend-color.florentine, .mobile-legend-color.florentine {
    background-color: #e54b21;
}

.legend-color.shah, .mobile-legend-color.shah {
    background-color: #127e5e;
}

/* hide mobile legend on desktop default */
.mobile-legend {
    display: none;
}

@media (max-width: 768px) {
    .mobile-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px
    }

    .legend {
        display: none
    }
}

.mobile-legend-item, .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem
}

.mobile-legend-color, .legend-color {
    width: 18px;
    height: 18px;
    border-radius: 3px
}

/* Gallery Section */
.gallery-section {
    position: relative;
    background: white;
    overflow: hidden;
}

/* Header */
.gallery-header {
    display: none;
}

.gallery-title {
    position: absolute;
    left: 38px;
    top: 14px;
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    line-height: 48px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Gallery Content */
.gallery-section .section-header {
    margin-bottom: 0px; /* tighter gap below header */
}

.gallery-content {
    padding: 20px 0;
}

.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.gallery-scroll-wrapper {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    -ms-overflow-style: none;
    will-change: transform;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.gallery-scroll-wrapper::-webkit-scrollbar {
    height: 8px;
}

.gallery-scroll-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.gallery-scroll-wrapper::-webkit-scrollbar-thumb {
    background: #102649;
    border-radius: 4px;
}

.gallery-scroll-wrapper::-webkit-scrollbar-thumb:hover {
    background: #007DB6;
}

.gallery-grid {
    display: flex;
    gap: 13px;
    width: max-content;
    padding-bottom: 20px;
}

.gallery-item {
    flex-shrink: 0;
    width: 291px;
    height: 520px;
    overflow: hidden;
    background: #D9D9D9;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    padding: 0 10px;
}

.gallery-nav-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(16, 38, 73, 0.8);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    pointer-events: auto;
}

.gallery-nav-btn:hover {
    background: #007DB6;
    transform: scale(1.1);
}

.gallery-nav-btn:active {
    transform: scale(0.95);
}

/* Gallery Dots */
.gallery-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
    padding: 10px;
}

.gallery-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ccc;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-dot:hover {
    background: #007DB6;
}

.gallery-dot.active {
    background: #102649;
    width: 24px;
    border-radius: 5px;
}

/* Play/Pause Button */
.gallery-play-pause {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(16, 38, 73, 0.8);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    pointer-events: auto;
}

.gallery-play-pause:hover {
    background: #007DB6;
}

.gallery-play-pause .play-icon {
    display: none;
}

.gallery-play-pause .pause-icon {
    display: block;
}

.gallery-play-pause:not(.playing) .play-icon {
    display: block;
}

.gallery-play-pause:not(.playing) .pause-icon {
    display: none;
}

/* Disable scrollbar when using navigation */
.gallery-scroll-wrapper.has-nav {
    scrollbar-width: none;
}

.gallery-scroll-wrapper.has-nav::-webkit-scrollbar {
    display: none;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .gallery-nav-btn {
        width: 40px;
        height: 40px;
    }

    .gallery-dots {
        margin-top: 15px;
    }

    .gallery-dot {
        width: 8px;
        height: 8px;
    }

    .gallery-dot.active {
        width: 20px;
    }
}

/* Touch device optimizations */
@media (hover: none) {
    .gallery-nav-btn {
        opacity: 0.7;
    }
}

/* Smooth transitions */
.gallery-scroll-wrapper {
    scroll-behavior: smooth;
}

/* Prevent text selection while dragging */
.gallery-container.dragging {
    user-select: none;
    -webkit-user-select: none;
}

/* Responsive Design */
@media (max-width: 1100px) {
    .gallery-container {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .gallery-title {
        font-size: 20px;
        left: 20px;
    }

    .gallery-content {
        padding: 20px 0;
    }

    .gallery-item {
        width: 250px;
        height: 446px;
    }

    .gallery-grid {
        gap: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 480px) {
    .gallery-item {
        width: 200px;
        height: 357px;
    }

    .gallery-grid {
        gap: 8px;
    }
}

/* Optional: Lightbox functionality styles */
.gallery-lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.gallery-lightbox.active {
    display: flex;
}

.gallery-lightbox img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
}

.gallery-lightbox-close {
    position: absolute;
    top: 30px;
    right: 30px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.gallery-lightbox-close:hover {
    color: #007DB6;
}

/* Responsive Design */
@media (max-width: 1100px) {
    .venue-container {
        padding: 0 20px;
    }
}

@media (max-width: 992px) {
    .venue-container {
        flex-direction: column;
        height: auto;
        align-items: center;
    }

    .venue-image-left,
    .venue-image-right {
        width: 100%;
        max-width: 600px;
        height: 392px; /* Same height for both containers on tablets */
        margin-left: 0;
    }

    .venue-image-right {
        margin-top: 20px;
    }

    .venue-image-right iframe {
        height: 100%;
        width: 100%;
        border: none;
    }
}

@media (max-width: 600px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on mobile */
    }
}

@media (max-width: 480px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on smaller phones */
    }
}

.event-venue-section > .container,
.gallery-section > .container,
.contact-section > .container {
    max-width: 1200px;
    padding: 0 20px;
}

/* Responsive Navigation Styles */
@media (max-width: 768px) {
    /* Maintain height variables in collapsed state */
    :root {
        --top-header-height: auto;
        --bottom-nav-height: auto;
    }

    /* Top navbar responsive adjustments */
    .navbar-collapse {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 8px;
        margin-top: 10px;
        padding: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid #dee2e6;
    }

    /* Ensure top navbar links are properly styled on mobile */
    .navbar .nav-link {
        padding: 12px 0;
        font-size: 16px;
        color: #333 !important;
        border-bottom: 1px solid #f0f0f0;
    }

    .navbar .nav-link:hover {
        color: #007db6 !important;
        background: rgba(0, 125, 182, 0.1);
        padding-left: 10px;
        border-radius: 4px;
    }

    .navbar .nav-item:last-child .nav-link {
        border-bottom: none;
    }

    /* Bottom navbar responsive adjustments */
    .nav-bar .navbar-collapse {
        background: rgba(16, 38, 73, 0.98);
        border-radius: 8px;
        margin-top: 10px;
        padding: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }

    .nav-bar .navbar-nav {
        width: 100%;
    }

    .nav-bar .nav-item {
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-bar .nav-item:last-child {
        border-bottom: none;
    }

    .nav-bar .nav-link {
        padding: 12px 0;
        font-size: 16px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .nav-bar .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
        padding-left: 10px;
    }

    /* Button adjustments for mobile */
    .nav-bar .d-flex {
        width: 100%;
        flex-direction: column;
        gap: 10px;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-bar .btn {
        display: block;
        width: 100%;
        margin: 0 0 10px 0;
        padding: 8px;
        text-align: center;
    }

    .nav-bar .logout-link {
        display: block;
        width: 100%;
        padding: 5px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        margin-top: 10px;
    }

    /* Smooth slide animation */
    .nav-bar-links,
    .nav-bar-actions {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .nav-bar-links.active {
        max-height: 500px;
        display: block;
    }

    .nav-bar-actions.active {
        max-height: 300px;
        display: block;
    }

    /* Prevent body scroll when mobile menu is open */
    body.navbar-open {
        overflow: hidden;
    }

    /* Z-index management for overlapping menus */
    .navbar {
        z-index: 1030;
    }

    .nav-bar {
        z-index: 1020;
    }

    /* Backdrop for mobile menus */
    .navbar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1010;
        display: none;
    }

    .navbar-backdrop.show {
        display: block;
    }
}

/* contact button responsive */
.contact-btn {
    width: auto;
}

@media (max-width: 768px) {
    .contact-btn {
        width: 100%;
        text-align: center;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .centered-features {
        flex-direction: column;
        align-items: stretch;
    }

    .centered-features .feature-card {
        flex: 0 1 100%;
        max-width: 100%;
        width: 100%;
    }
}

/* Join Section */
.join-section {
    padding: 40px 0;
    margin-bottom: 30px;
}

.join-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px;
    background: #102649;
    padding: 40px;
    border-radius: 0px;
    max-width: 1125px;
    margin: 0 auto;
}

.join-text-wrapper {
    max-width: 550px;
}

.join-heading {
    color: #ffffff;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 48px;
    margin: 0 0 20px 0;
}

.join-text {
    color: #ffffff;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
    text-transform: capitalize;
    line-height: 24px;
    margin: 0;
}

.join-btn {
    background: #F7C100;
    color: #102649;
    font-size: 20px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    padding: 8px 48px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.join-btn:hover,
.join-btn:focus,
.join-btn:active {
    background: #F7C100 !important;
    color: #102649 !important;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    filter: none !important;
    opacity: 1 !important;
    text-decoration: none !important;
    transition: none !important;
}

@media (max-width: 768px) {
    .join-container {
        flex-direction: column;
        text-align: center;
        padding: 30px 20px;
    }

    .join-text-wrapper {
        max-width: 100%;
    }

    .join-heading {
        font-size: 22px;
        line-height: 1.3;
        margin-bottom: 15px;
    }

    .join-text {
        font-size: 15px;
        line-height: 1.4;
        margin-bottom: 20px;
    }

    .join-btn {
        margin-top: 10px;
        align-self: center;
        width: 100%;
        max-width: 250px;
        padding: 10px 30px;
    }
}

@media (max-width: 480px) {
    .join-container {
        padding: 25px 15px;
    }

    .join-heading {
        font-size: 20px;
    }

    .join-text {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    /* ensure container stacks vertically when menu opened */
    .nav-bar .container {
        flex-direction: column;
        align-items: stretch;
    }

    /* make collapse block for full width */
    .nav-bar.navbar .navbar-collapse {
        width: 100%;
    }

    /* fix mobile collapse show */
    .nav-bar.navbar .navbar-collapse.show {
        display: block !important;
    }
}

@media (max-width: 768px) {
    .hero {
        /* Adjust hero height for mobile and avoid using variables that become 'auto' */
        height: 60vh;
    }

    /* Top navbar mobile adjustments */
    .navbar-collapse#mainNavbar {
        /* Allow scrolling for long menus on small screens */
        max-height: 70vh;
        overflow-y: auto;
        padding: 1rem 0;
    }

    #mainNavbar .navbar-nav {
        align-items: flex-start !important;
    }

    #mainNavbar .nav-item {
        margin-bottom: 0.5rem;
    }

    #mainNavbar .btn {
        margin-top: 0.5rem;
        width: 100%;
    }

    /* Bottom navbar mobile adjustments */
    .nav-bar .navbar-collapse#eventNavbar {
        padding-top: 1rem;
        /* Allow scrolling for long menus on small screens */
        max-height: 70vh;
        overflow-y: auto;
    }

    .nav-bar-toggle {
        display: none;
        flex-direction: column;
        justify-content: space-around;
        width: 30px;
        height: 24px;
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 0;
        z-index: 1001;
    }

    .hamburger-line {
        width: 100%;
        height: 3px;
        background-color: #fff;
        border-radius: 2px;
        transition: all 0.3s ease;
        transform-origin: center;
    }

    /* Hamburger Animation */
    .nav-bar-toggle.active .hamburger-line:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .nav-bar-toggle.active .hamburger-line:nth-child(2) {
        opacity: 0;
    }

    .nav-bar-toggle.active .hamburger-line:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    /* Mobile Navigation Styles */
    @media (max-width: 768px) {
        /* Show toggle button on mobile */
        .nav-bar-toggle {
            display: flex;
        }

        /* Container adjustments */
        .nav-bar .container {
            position: relative;
            flex-direction: column;
            align-items: flex-start;
        }

        /* Mobile Header Row */
        .nav-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 62px;
            background: #102649;
            z-index: 999;
        }

        .nav-bar-toggle {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1001;
        }

        /* Hide navigation by default on mobile */
        .nav-bar-links,
        .nav-bar-actions {
            display: none;
            width: 100%;
            background: #102649;
            position: absolute;
            top: 65px;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Show navigation when active */
        .nav-bar-links.active,
        .nav-bar-actions.active {
            display: block;
        }

        /* Mobile navigation links styling */
        .nav-bar-links {
            flex-direction: column;
            padding: 20px 0;
        }

        .nav-bar-links a {
            display: block;
            padding: 15px 20px;
            margin: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .nav-bar-links a:last-child {
            border-bottom: none;
        }

        .nav-bar-links a:hover {
            background: rgba(255, 255, 255, 0.1);
            padding-left: 30px;
        }

        /* Mobile action buttons styling */
        .nav-bar-actions {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-bar-actions .btn {
            display: block;
            width: 100%;
            margin: 0 0 10px 0;
            padding: 8px;
            text-align: center;
        }

        .nav-bar-actions .logout-link {
            display: block;
            width: 100%;
            padding: 5px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin-top: 10px;
        }

        /* Smooth slide animation */
        .nav-bar-links,
        .nav-bar-actions {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .nav-bar-links.active {
            max-height: 500px;
            display: block;
        }

        .nav-bar-actions.active {
            max-height: 300px;
            display: block;
        }

        /* Prevent body scroll when menu is open */
        body.nav-open {
            overflow: hidden;
        }
    }

    /* Desktop styles - ensure proper display */
    @media (min-width: 769px) {
        .nav-bar-toggle {
            display: none;
        }

        .nav-bar .container {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .nav-bar-links,
        .nav-bar-actions {
            display: flex !important;
            position: static;
            background: none;
            box-shadow: none;
            padding: 0;
            max-height: none;
        }

        .nav-bar-links {
            flex: 1;
            justify-content: center;
        }

        .nav-bar-actions {
            flex-direction: row;
            align-items: center;
        }
    }

    #eventNavbar .d-flex {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    #eventNavbar .d-flex .btn {
        width: 100%;
        margin: 0;
    }

    .event-details,
    .benefits,
    .agenda-section,
    .speakers,
    .tickets,
    .sponsors-section,
    .event-venue-section,
    .gallery-section,
    .contact-section {
        padding-bottom: 30px;
    }

    .section-header,
    .tickets-header-title,
    .venue-title,
    .gallery-title,
    .sponsors-header-section .section-header,
    .contact-header {
        font-size: 20px;
        padding-left: 20px;
        height: 60px;
    }

    .event-content {
        grid-template-columns: 1fr;
    }

    .event-text h2 {
        font-size: 24px;
    }

    .event-features {
        grid-template-columns: 1fr;
    }

    .centered-features {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .join-container {
        flex-direction: column;
        text-align: center;
    }

    .join-heading {
        font-size: 24px;
    }

    .join-btn {
        margin-top: 20px;
    }

    .speakers-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .tickets-grid {
        grid-template-columns: 1fr;
    }

    .sponsors-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .venue-content {
        flex-direction: column;
    }

    .venue-map {
        height: 300px;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-container {
        flex-direction: column;
    }
}

/* Time headers matching stage colors */
.time-cell[data-time] {
    position: relative;
}

/* Use nth-child to map stage column background? Not feasible. Instead, keep separate. */

/* === Section header background fix === */
.section-header,
.tickets-header-section,
.venue-header,
.gallery-header,
.sponsors-header-section,
.contact-header {
    background: #102649 !important;
    max-width: 1200px;
    margin: 0 auto 20px;
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
}

.section-header,
.tickets-header-title,
.venue-title,
.gallery-title,
.sponsors-header-section .section-header,
.contact-header {
    color: #ffffff !important;
}


/* ===== Content-width white backgrounds ===== */
section.event-details,
section.benefits,
section.speakers,
section.agenda-section,
section.tickets,
section.gallery-section,
section.sponsors-section,
section.event-venue-section,
section.contact-section {
    background: transparent !important; /* remove full-width white */
}

.event-details > .container,
.speakers > .container,
.agenda-section > .container,
.tickets > .container,
.gallery-section > .container,
.sponsors-section > .container,
.event-venue-section > .container,
.contact-section > .container {
    background: #ffffff;
}

@media (max-width: 768px) {
    .venue-container {
        flex-direction: column;
        align-items: center;
    }

    .venue-image-left,
    .venue-image-right {
        width: 100%;
        max-width: 600px;
        height: 392px; /* Same height for both containers on tablets */
        margin-left: 0;
    }

    .venue-image-right {
        margin-top: 20px;
    }

    .venue-image-right iframe {
        height: 100%;
        width: 100%;
        border: none;
    }
}

@media (max-width: 600px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on mobile */
    }
}

@media (max-width: 480px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px; /* Same height for both containers on smaller phones */
    }
}

@media (max-width: 600px) {
    .venue-image-left,
    .venue-image-right {
        height: 392px !important; /* Force same height with !important */
        margin: 0 0 15px 0; /* Bottom margin only */
        border-radius: 0; /* Remove border radius on mobile */
        width: 100%; /* Full width */
        max-width: none; /* Override any max-width */
    }

    .venue-image-right iframe {
        height: 392px !important; /* Force exact height */
    }
}

section.contact-section {
    padding: 40px 0; /* Reduced top padding from 80px to 40px */
}
