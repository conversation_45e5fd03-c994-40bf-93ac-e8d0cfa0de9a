<div class="visitor-name-tag">
    {{-- Expose Livewire component id for JS lookup --}}
    <script>
        window.visitorNameTagComponentId = '{{ $this->id() }}';
    </script>


    @php
        // Load country data for WhatsApp field
        $countries = json_decode(file_get_contents(resource_path('Data/country.json')), true);
        $visitorCountryCode = strtoupper($visitor->country_code ?? 'EG');
        $visitorCallingCode = '';
        $visitorCountryFlag = 'fi fi-' . strtolower($visitorCountryCode);
        $whatsappCountryCodeVal = strtoupper($visitor->whatsapp_country_code ?? $visitorCountryCode);
        $whatsappCallingCode = '';
        if (isset($countries) && is_array($countries)) {
            foreach ($countries as $c) {
                if (strtoupper($c['code']) === $visitorCountryCode) {
                    $visitorCountryFlag = 'fi fi-' . strtolower($c['code']);
                    $visitorCallingCode = $c['calling_code'];
                }
                if (strtoupper($c['code']) === $whatsappCountryCodeVal) {
                    $whatsappCallingCode = $c['calling_code'];
                }
            }
        }
    @endphp



        <div class="ticket-container">
        <div class="ticket-header">
            <div class="ticket-title">
                <span class="my-text">MY</span> <span class="ticket-text">TICKET</span>
            </div>
            <p>Congratulations your Name Tag is ready, Share it now</p>
        </div>

        <div class="ticket-content">
            <div class="form-section">
                <form wire:submit.prevent="update">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="ticket-type">Ticket Type*</label>
                            <input type="text" id="ticket-type" name="ticket_type"
                                   value="{{ $ticket->ticket->name ?? 'N/A' }}" readonly>
                        </div>
                        <div class="form-group">
                            <label for="workshops">Workshops*</label>
                            <select id="workshops" class="workshops-select" name="workshops"
                                    wire:model="selectedWorkshops" @if($selectedWorkshops) style="  background: rgba(202, 208, 215, 0.42);" disabled @endif>
                                <option value="">Please select a workshop</option>
                                @foreach($workshops as $workshop)
                                    <option
                                        value="{{ $workshop->id }}" {{ $workshop->id == $selectedWorkshops ? 'selected' : '' }}>
                                        {{ $workshop->name ?? ('ID: ' . $workshop->id . ' - Name Missing') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="first-name">First Name*</label>
                            <input type="text" id="first-name" name="first_name" wire:model="firstName"
                                   value="{{ $visitor->first_name }}">
                        </div>
                        <div class="form-group">
                            <label for="last-name">Last Name*</label>
                            <input type="text" id="last-name" name="last_name" wire:model="lastName"
                                   value="{{ $visitor->second_name }}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="company-name">Company Name*</label>
                            <input type="text" id="company-name" name="company_name" wire:model="companyName"
                                   value="{{ $visitor->company }}">
                        </div>
                        <div class="form-group">
                            <label for="whatsapp-number">What's App Number*</label>
                            <div class="input-group rounded-3">
                                <div
                                    x-data="countryDropdown()"
                                    x-init="initCountryDropdown('{{ strtolower($whatsappCountryCodeVal) }}', '{{ $whatsappCallingCode }}', true)"
                                    @whatsapp-country-updated.window="updateSelectedCountry($event.detail.countryCode, $event.detail.callingCode)"
                                    class="country-dropdown-container whatsapp-country-dropdown"
                                >
                                    <button
                                        type="button"
                                        @click="toggleDropdown"
                                        class="btn country-select-button"
                                    >
                                        <div class="d-flex align-items-center">
                                            <span :class="'fi fi-' + selectedCountryCode"></span>
                                            <span><i class="bi bi-chevron-down ms-1"></i></span>
                                        </div>
                                        <span x-text="'+' + selectedCallingCode"></span>
                                    </button>

                                    <div
                                        x-show="isOpen"
                                        @click.away="isOpen = false"
                                        class="country-dropdown"
                                        x-transition
                                    >
                                        <div class="country-search-container">
                                            <input
                                                type="text"
                                                x-model="searchQuery"
                                                @input="filterCountries"
                                                placeholder="Search countries..."
                                                class="form-control form-control-sm"
                                            >
                                        </div>
                                        <div class="country-list">
                                            @foreach($countries as $countryOption)
                                                <div
                                                    class="country-item"
                                                    x-show="isVisible('{{ $countryOption['name'] }}', '{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                                    @click="selectCountry('{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}', true)"
                                                >
                                                    <span class="fi fi-{{ strtolower($countryOption['code']) }} me-2"></span>
                                                    <span>{{ $countryOption['name'] }} (+{{ $countryOption['calling_code'] }})</span>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    <input type="hidden" wire:model="whatsappCountryCode" id="whatsapp-country-hidden-input">
                                </div>

                                <input type="tel" id="whatsapp-number" name="whatsapp_number"
                                       wire:model="whatsappNumber" class="form-control py-3" placeholder="Enter Your Whatsapp">
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="job-title-1">Job Title (Part 1)*</label>
                            <input type="text" id="job-title-1" name="job_title_1" wire:model="jobTitle1"
                                   value="{{ $visitor->title1 }}">
                        </div>
                        <div class="form-group">
                            <label for="job-title-2">Job Title (Part 2)*</label>
                            <input type="text" id="job-title-2" name="job_title_2" wire:model="jobTitle2"
                                   value="{{ $visitor->title2 }}">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="linkedin-profile">LinkedIn Profile*</label>
                            <input type="url" id="linkedin-profile" name="linkedin_profile" wire:model="linkedinProfile"
                                   value="{{ $visitor->linkedin }}">
                        </div>
                        <div class="form-group">
                            <label>Upload your professional photo</label>
                            <div class="d-flex flex-column flex-md-row align-items-center">
                                <div class="me-md-4 mb-3 mb-md-0 flex-grow-1">
                                    <div class="file-upload">
                                        <button type="button" class="choose-file-btn"
                                                onclick="document.querySelector('#profilePhotoInput').click()">Choose
                                            File
                                        </button>
                                        <span class="file-name" id="file-name-display">
                                            @if(!$visitor->image && !$newPhoto)
                                                No File Chosen
                                            @else
                                                Photo Uploaded
                                            @endif
                                        </span>
                                        <!-- Separate inputs: one for JS cropping (#profilePhotoInput) and one hidden bound to Livewire -->
                                        <input type="file" id="profilePhotoInput" accept="image/*" class="d-none">
                                        <input type="file" id="photo-upload" wire:model="newPhoto" class="d-none">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="update-btn">Update</button>
                </form>

                <div class="ticket-info">
                    <h3>More Information</h3>
                    <div class="info-items-container">
                        <div class="info-item">
                            <label>Status</label>
                            <p>{{ $ticket->payment_status == 1 ? 'Paid' : 'Waiting for Purchase' }}</p>
                        </div>
                        <div class="info-item">
                            <label>Payment ID</label>
                            <p>{{ $ticket->payment_id ?? 'N/A' }}</p>
                        </div>
                        <div class="info-item">
                            <label>Confirmation Number</label>
                            <p>{{ $visitor->qr_code ?? 'N/A' }}</p>
                        </div>
                        <div class="info-item">
                            <label>Ticket Name</label>
                            <p>{{ $ticket->ticket->name ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-preview">
                    @if($visitor->name_tag)
                        <div class="name-tag-image-container">
                            <img src="{{ $visitor->event_name_tag }}" alt="Name Tag" class="name-tag-image">
                        </div>
                    @else
                        @if($visitor->image)
                            <img src="{{ asset('storage/visitor_images/' . $visitor->image) }}"
                                 alt="Visitor Photo">
                        @else
                            <img src="{{ asset('assets/img/avatars/blank-profile.png') }}" alt="Default Profile Image">
                        @endif
                    @endif
                <div class="share-section">
                    @livewire('share-on-linkedin', ['visitor' => $visitor])
                </div>
            </div>
        </div>
    </div>
    <!-- Image Cropper Modal -->
    <div class="modal fade" id="imageCropperModal" tabindex="-1" aria-labelledby="imageCropperModalLabel" role="dialog"
         aria-modal="true" data-bs-backdrop="true" data-bs-keyboard="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageCropperModalLabel">Crop Your Profile Photo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <small class="text-muted">Drag to move, scroll to zoom, and resize the crop area as
                            needed.</small>
                    </div>
                    <div class="img-container" style="height: 400px; max-width: 100%; overflow: hidden;">
                        <img id="imageToCrop" src="" alt="Image to crop" style="max-width: 100%; display: block;">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="cropButton">
                        <i class="bi bi-crop"></i> Crop & Save
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>
