<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class FilamentAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin permission
        $permission = Permission::firstOrCreate(['name' => 'access_admin_panel']);
        
        // Create admin role and assign permission
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo($permission);
        
        // Check if admin user exists, create if not
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $admin = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'), // Change this to a secure password in production
                'email_verified_at' => now(),
            ]);
        }
        
        // Assign admin role to user
        $admin->assignRole($adminRole);
    }
}
