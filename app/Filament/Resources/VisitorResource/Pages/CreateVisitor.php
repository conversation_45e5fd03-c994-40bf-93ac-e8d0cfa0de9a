<?php

namespace App\Filament\Resources\VisitorResource\Pages;

use App\Filament\Resources\VisitorResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Hash;

class CreateVisitor extends CreateRecord
{
    protected static string $resource = VisitorResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Generate a random password for new visitors if needed
        if (!isset($data['password'])) {
            $data['password'] = Hash::make(str_random(12));
        }
        
        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
