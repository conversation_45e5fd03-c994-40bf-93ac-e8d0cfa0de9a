<?php

namespace App\Filament\Resources\VisitorResource\Pages;

use App\Filament\Resources\VisitorResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewVisitor extends ViewRecord
{
    protected static string $resource = VisitorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('downloadNameTag')
                ->label('Download Name Tag')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('primary')
                ->url(fn () => $this->record->event_name_tag ? asset($this->record->event_name_tag) : null)
                ->openUrlInNewTab()
                ->visible(fn () => !empty($this->record->event_name_tag)),
        ];
    }
}
