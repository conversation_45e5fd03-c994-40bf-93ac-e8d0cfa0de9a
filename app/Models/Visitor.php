<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Notifications\VisitorVerifyEmail;
use Laravel\Sanctum\HasApiTokens;

class Visitor extends Authenticatable implements MustVerifyEmail
{
    use HasFactory,Notifiable,SoftDeletes, HasApiTokens;
    protected $guard = 'visitor';
    protected $guarded = ['id'];

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new VisitorVerifyEmail);
    }

    public function tickets (): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Ticket::class,'visitor_tickets')->withPivot('id','payment_status','payment_id','approved','note');
    }

    public function agendas (): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Agenda::class,'visitor_agendas')->withPivot('attend','confirmed');
    }
    public function visitorTickets (): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(VisitorTicket::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class)->withDefault();
    }
    public function activities ()
    {
        return $this->hasMany(SalesActivity::class)->orderBy('created_at','desc');
    }
    // public function setImageAttribute($value)
    // {
        // if (!empty($value)) {
        //     $filename = $value->getClientOriginalName();
        //     $location = storage_path('app/public/visitors');
        //     $value->move($location, $filename);
        //     $this->attributes['image'] = $filename;
        // }
    // }
    public function getSpeakerImageAttribute($value)
    {
        if (!$value) {
            return null;
        }

        // If already stored with folder prefix, return as is
        if (str_starts_with($value, 'images/speakers/')) {
            return $value;
        }

        return 'images/speakers/' . ltrim($value, '/');
    }

    /**
     * Get the formatted image URL for display in Filament.
     *
     * @return string|null
     */
    public function getFormattedImageAttribute()
    {
        if (empty($this->image)) {
            return null;
        }
        
        // Check if image exists in the public storage
        if (file_exists(public_path('storage/visitor_images/' . $this->image))) {
            return 'storage/visitor_images/' . $this->image;
        }
        
        return null;
    }
}
