<?php

namespace App\Services;

use App\Models\Agenda;
use App\Models\Visitor;
use App\Models\VisitorTicket;
use App\ValueObjects\VisitorFormData;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

/**
 * Class VisitorRegistrationService
 * @package App\Services
 */
class VisitorRegistrationService
{
    /**
     * Get workshops for a specific day
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getWorkshops()
    {
        return Agenda::get();
    }

    /**
     * Validate a registration code
     *
     * @param string $code
     * @return bool
     */
    public function validateCode(string $code): bool
    {
        $ticket = VisitorTicket::where('code', $code)->first();
        return $ticket && $ticket->available_visitor >= 1;
    }

    /**
     * Get note for visitor based on code
     *
     * @param string|null $code
     * @return string|null
     */
    public function getVisitorNote(?string $code): ?string
    {
        if (!$code) {
            return null;
        }

        $ticket = VisitorTicket::where(['code' => $code])->first();
        if (!$ticket || $ticket->available_visitor < 1) {
            return null;
        }

        return 'Corporate Ticket, Invited by ' . $ticket->visitor->name . ', ' . $ticket->visitor->company_name;
    }

    /**
     * Register a new visitor without sending verification email
     *
     * @param VisitorFormData $formData
     * @param TemporaryUploadedFile|null $profilePhoto
     * @return Visitor
     */
    public function registerWithoutVerification(VisitorFormData $formData, ?TemporaryUploadedFile $profilePhoto = null): Visitor
    {
        $profilePhotoPath = null;
        if ($profilePhoto) {
            $fullPath = $profilePhoto->store('visitor_images', 'public');
            // Extract only the filename from the full path
            $profilePhotoPath = basename($fullPath);
        }

        $data = $formData->toArray();
        $data['image'] = $profilePhotoPath;
        $data['password'] = Hash::make($data['password']);

        // Get note based on code
        $data['note'] = $this->getVisitorNote($data['corporate_code']);

        // Create the visitor
        return Visitor::create($data);
    }

    /**
     * Send verification email to the visitor
     *
     * @param Visitor $visitor
     * @return void
     */
    public function sendVerificationEmail(Visitor $visitor): void
    {
        event(new Registered($visitor));
    }

    /**
     * Register a new visitor
     *
     *
     * @param VisitorFormData $formData
     * @param TemporaryUploadedFile|null $profilePhoto
     * @return Visitor
     * @deprecated Use registerWithoutVerification() and sendVerificationEmail() separately
     */
    public function register(VisitorFormData $formData, ?TemporaryUploadedFile $profilePhoto = null): Visitor
    {
        $visitor = $this->registerWithoutVerification($formData, $profilePhoto);
        $this->sendVerificationEmail($visitor);
        return $visitor;
    }
}
