<?php

namespace App\Services;

use App\Models\Visitor;
use App\Models\VisitorTicket;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver as GdDriver;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use Intervention\Image\Typography\FontFactory;

class TagNameTagGenerator
{
    // File paths
    private const TEMPLATE_CLASSIC = 'images/Post Social Media.png';
    private const TEMPLATE_NEW = 'images/nametaglast.png';
    private const FONT_BOLD = 'fonts/GothamBold.ttf';
    private const FONT_REGULAR = 'fonts/GothamMedium.ttf';
    private const PLACEHOLDER = 'images/placeholder-nametag.png';

    // Directory paths
    private const DIR_NAME_TAGS = 'images/name_tags';
    private const DIR_NEW_NAME_TAGS = 'images/new_name_tags';
    private const DIR_QR_CODES = 'qr-codes';
    private const DIR_VISITOR_IMAGES = 'app/public/visitor_images/';

    // Classic template dimensions
    private const CLASSIC_PHOTO_WIDTH = 470;
    private const CLASSIC_PHOTO_HEIGHT = 490;
    private const CLASSIC_PHOTO_Y = 300;
    private const CLASSIC_BORDER_WIDTH = 5;
    private const CLASSIC_BORDER_COLOR = '#8A5A18FF';
    private const CLASSIC_BORDER_RADIUS = 80;

    // New template dimensions
    private const NEW_PHOTO_WIDTH = 295;
    private const NEW_PHOTO_HEIGHT = 310;
    private const NEW_PHOTO_Y = 190;
    private const NEW_BORDER_WIDTH = 2;
    private const NEW_BORDER_COLOR = '#f0c000';
    private const NEW_BORDER_RADIUS = 20;

    // Text positioning
    private const CLASSIC_TEXT_OFFSET = 70;
    private const NEW_NAME_Y = 540;
    private const NEW_TITLE_Y = 620;
    private const NEW_COMPANY_Y = 600;

    // Colors
    private const COLOR_NAVY = '#102649';
    private const COLOR_NAVY_ALT = '#11294d';
    private const COLOR_WHITE = '#ffffff';

    // QR Code settings (only for new template)
    private const QR_SIZE = 103;
    private const QR_Y_POSITION = 740;

    // QR Code API settings
    private const QR_API_BASE_URL = 'https://api.qrserver.com/v1/create-qr-code/';
    private const QR_API_TIMEOUT = 15;

    private ImageManager $manager;

    /**
     * Generate classic name tag (NO QR CODE)
     */
    public function generateNameTag(Visitor $visitor, VisitorTicket $ticket): string
    {
        Log::info('Starting classic name tag generation', ['visitor_id' => $visitor->id]);

        if (!$this->initializeImageManager()) {
            return $this->getPlaceholderUrl();
        }

        try {
            $templatePath = public_path(self::TEMPLATE_CLASSIC);
            $img = $this->loadTemplate($templatePath);

            $fullName = $this->prepareFullName($visitor);

            $this->addClassicPhoto($img, $visitor);
            $this->addClassicTextElements($img, $visitor, $fullName);
            // NO QR CODE FOR CLASSIC TEMPLATE

            return $this->saveNameTag($img, $visitor, 'nametag_', self::DIR_NAME_TAGS);

        } catch (\Exception $e) {
            return $this->handleError('classic name tag', $e, $visitor->id);
        }
    }

    /**
     * Generate new name tag (WITH QR CODE)
     */
    public function generateNewNameTag(Visitor $visitor, VisitorTicket $ticket): string
    {
        Log::info('Starting new name tag generation', ['visitor_id' => $visitor->id]);

        if (!$this->initializeImageManager()) {
            return $this->getPlaceholderUrl();
        }

        try {
            $templatePath = public_path(self::TEMPLATE_NEW);
            $img = $this->loadTemplate($templatePath);

            $fullName = $this->prepareFullName($visitor);

            $this->addNewPhoto($img, $visitor);
            $this->addNewTextElements($img, $visitor, $fullName);
            $this->addLinkedInQrCode($img, $visitor); // ONLY ADD QR CODE TO NEW TEMPLATE

            return $this->saveNameTag($img, $visitor, 'new_nametag_', self::DIR_NEW_NAME_TAGS);

        } catch (\Exception $e) {
            return $this->handleError('new name tag', $e, $visitor->id);
        }
    }

    /**
     * Initialize image manager with best available driver
     */
    private function initializeImageManager(): bool
    {
        $this->manager = $this->createImageManager();

        if (!$this->manager) {
            Log::error('Could not create image manager - no suitable PHP extensions available');
            return false;
        }

        return true;
    }

    /**
     * Create image manager with fallback drivers
     */
    private function createImageManager(): ?ImageManager
    {
        // Try Imagick first
        try {
            if (extension_loaded('imagick')) {
                return new ImageManager(new ImagickDriver());
            }
        } catch (\Exception $e) {
            Log::warning('Imagick driver failed: ' . $e->getMessage());
        }

        // Try GD as fallback
        try {
            if (extension_loaded('gd')) {
                return new ImageManager(new GdDriver());
            }
        } catch (\Exception $e) {
            Log::warning('GD driver failed: ' . $e->getMessage());
        }

        Log::error('No image processing driver available');
        return null;
    }

    /**
     * Load and validate template
     */
    private function loadTemplate(string $templatePath)
    {
        if (!$this->validateRequiredFiles($templatePath)) {
            throw new \Exception('Template or font files not found');
        }

        return $this->manager->read($templatePath);
    }

    /**
     * Validate required files exist
     */
    private function validateRequiredFiles(string $templatePath): bool
    {
        $requiredFiles = [
            $templatePath,
            public_path(self::FONT_BOLD),
            public_path(self::FONT_REGULAR)
        ];

        foreach ($requiredFiles as $file) {
            if (!file_exists($file)) {
                Log::error('Required file not found: ' . $file);
                return false;
            }
        }

        return true;
    }

    /**
     * Prepare visitor's full name
     */
    private function prepareFullName(Visitor $visitor): string
    {
        $firstName = trim($visitor->first_name ?? '');
        $secondName = trim($visitor->second_name ?? '');

        if (empty($firstName) && empty($secondName)) {
            return "Valued Visitor";
        }

        return trim($firstName . ' ' . $secondName);
    }

    /**
     * Add photo for classic template (NO QR CODE)
     */
    private function addClassicPhoto($img, Visitor $visitor): void
    {
        if (!$visitor->image) {
            return;
        }

        $photoPath = $this->getVisitorPhotoPath($visitor->image);

        if (!file_exists($photoPath)) {
            return;
        }

        try {
            $photo = $this->processClassicPhoto($photoPath);
            $this->placePhotoOnTemplate($img, $photo, self::CLASSIC_PHOTO_WIDTH, self::CLASSIC_PHOTO_Y, self::CLASSIC_BORDER_WIDTH);
        } catch (\Exception $e) {
            Log::error('Classic photo placement error: ' . $e->getMessage());
        }
    }

    /**
     * Add photo for new template
     */
    private function addNewPhoto($img, Visitor $visitor): void
    {
        if (!$visitor->image) {
            return;
        }

        $photoPath = $this->getVisitorPhotoPath($visitor->image);

        if (!file_exists($photoPath)) {
            return;
        }

        try {
            $photo = $this->processNewPhoto($photoPath);
            $this->placePhotoOnTemplate($img, $photo, self::NEW_PHOTO_WIDTH, self::NEW_PHOTO_Y, self::NEW_BORDER_WIDTH);
        } catch (\Exception $e) {
            Log::error('New photo placement error: ' . $e->getMessage());
        }
    }

    /**
     * Get visitor photo path with fallback
     */
    private function getVisitorPhotoPath(string $imageName): string
    {
        $primaryPath = storage_path(self::DIR_VISITOR_IMAGES . $imageName);

        if (file_exists($primaryPath)) {
            return $primaryPath;
        }

        return storage_path('app/public/' . $imageName);
    }

    /**
     * Process photo for classic template
     */
    private function processClassicPhoto(string $photoPath)
    {
        $photo = $this->manager->read($photoPath);

        $photo = $this->cropAndResizePhoto($photo, self::CLASSIC_PHOTO_WIDTH, self::CLASSIC_PHOTO_HEIGHT);
        $photo->greyscale();

        $photo = $this->addPhotoBorder($photo, self::CLASSIC_PHOTO_WIDTH, self::CLASSIC_PHOTO_HEIGHT,
            self::CLASSIC_BORDER_WIDTH, self::CLASSIC_BORDER_COLOR);

        $this->applyRoundedCorners($photo, self::CLASSIC_BORDER_RADIUS);

        return $photo;
    }

    /**
     * Process photo for new template
     */
    private function processNewPhoto(string $photoPath)
    {
        $photo = $this->manager->read($photoPath);

        $photo = $this->cropAndResizePhoto($photo, self::NEW_PHOTO_WIDTH, self::NEW_PHOTO_HEIGHT);
        $photo->greyscale();
        $photo = $this->addPhotoBorder($photo, self::NEW_PHOTO_WIDTH, self::NEW_PHOTO_HEIGHT,
            self::NEW_BORDER_WIDTH, self::NEW_BORDER_COLOR);

        $this->applyRoundedCorners($photo, self::NEW_BORDER_RADIUS);

        return $photo;
    }

    /**
     * Crop and resize photo with consistent proportions
     */
    private function cropAndResizePhoto($photo, int $targetWidth, int $targetHeight)
    {
        $originalWidth = $photo->width();
        $originalHeight = $photo->height();

        $cropHeight = $originalHeight * 0.7;
        $cropWidth = $cropHeight;
        $cropX = ($originalWidth - $cropWidth) / 2;
        $cropY = $originalHeight * 0.1;

        $photo->crop((int)$cropWidth, (int)$cropHeight, (int)$cropX, (int)$cropY);
        $photo->resize($targetWidth, $targetHeight);

        return $photo;
    }

    /**
     * Add border to photo
     */
    private function addPhotoBorder($photo, int $width, int $height, int $borderWidth, string $borderColor)
    {
        $borderedPhoto = $this->manager->create(
            $width + ($borderWidth * 2),
            $height + ($borderWidth * 2)
        );

        $borderedPhoto->fill($borderColor);
        $borderedPhoto->place($photo, 'center');

        return $borderedPhoto;
    }

    /**
     * Place photo on template
     */
    private function placePhotoOnTemplate($img, $photo, int $photoWidth, int $photoY, int $borderWidth): void
    {
        $width = $img->width();
        $targetX = ($width - $photoWidth) / 2 - $borderWidth;
        $targetY = $photoY - $borderWidth;

        $img->place($photo, 'top-left', (int)$targetX, (int)$targetY);
    }

    /**
     * Add text elements for classic template
     */
    private function addClassicTextElements($img, Visitor $visitor, string $fullName): void
    {
        $width = $img->width();
        $photoBottom = self::CLASSIC_PHOTO_Y + self::CLASSIC_PHOTO_HEIGHT + 10;

        $nameY = $photoBottom + self::CLASSIC_TEXT_OFFSET;
        $companyY = $nameY + 90;
        $titleY = $companyY + 50;

        // Add visitor name
        $this->addText($img, $fullName, $width / 2, $nameY, [
            'font' => public_path(self::FONT_BOLD),
            'size' => 35,
            'color' => self::COLOR_NAVY
        ]);

        // Add company
        $this->addText($img, $visitor->company ?? '', $width / 2, $companyY, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 55,
            'color' => self::COLOR_NAVY
        ]);

        // Add title
        $this->addText($img, $visitor->title1 ?? '', $width / 2, $titleY, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 55,
            'color' => self::COLOR_NAVY
        ]);

        $this->addText($img, $visitor->title2 ?? '', $width / 2, $titleY + 55, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 55,
            'color' => self::COLOR_NAVY
        ]);
    }

    /**
     * Add text elements for new template
     */
    private function addNewTextElements($img, Visitor $visitor, string $fullName): void
    {
        $width = $img->width();

        // Add visitor name (white text)
        $this->addText($img, $fullName, $width / 2, self::NEW_NAME_Y, [
            'font' => public_path(self::FONT_BOLD),
            'size' => 20,
            'color' => self::COLOR_WHITE
        ]);

        // Add company
        $this->addText($img, $visitor->company ?? '', $width / 2, self::NEW_COMPANY_Y, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 20,
            'color' => self::COLOR_NAVY
        ]);
        // Add title (navy blue)
        $this->addText($img, $visitor->title1 ?? '', $width / 2, self::NEW_TITLE_Y, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 20,
            'color' => self::COLOR_NAVY_ALT
        ]);
        $this->addText($img, $visitor->title2 ?? '', $width / 2, self::NEW_TITLE_Y + 20, [
            'font' => public_path(self::FONT_REGULAR),
            'size' => 20,
            'color' => self::COLOR_NAVY_ALT
        ]);
    }

    /**
     * Add text to image
     */
    private function addText($img, string $text, float $x, float $y, array $options): void
    {
        $img->text($text, $x, $y, function (FontFactory $font) use ($options) {
            $font->filename($options['font']);
            $font->size($options['size']);
            $font->color($options['color']);
            $font->align('center');
            $font->valign('middle');
        });
    }

    /**
     * Add LinkedIn QR code ONLY to new template using third-party API
     */
    private function addLinkedInQrCode($img, Visitor $visitor): void
    {
        if (empty($visitor->linkedin)) {
            return;
        }

        try {
            $linkedinUrl = $this->validateAndFormatUrl($visitor->linkedin);
            $qrImageData = $this->generateQrCodeFromApi($linkedinUrl);

            if ($qrImageData) {
                $this->placeQrCodeOnImage($img, $qrImageData);
            }
        } catch (\Exception $e) {
            Log::error('LinkedIn QR code error: ' . $e->getMessage());
        }
    }

    /**
     * Validate and format LinkedIn URL
     */
    private function validateAndFormatUrl(string $url): string
    {
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            $url = 'https://' . $url;
        }

        return $url;
    }

    /**
     * Generate QR code using third-party API and return image data
     */
    private function generateQrCodeFromApi(string $url): ?string
    {
        try {
            $qrParams = [
                'size' => self::QR_SIZE . 'x' . self::QR_SIZE,
                'format' => 'png',
                'bgcolor' => 'FFFFFF',
                'color' => '000000',
                'qzone' => '1',
                'margin' => '0',
                'ecc' => 'H',
                'data' => $url
            ];

            $qrUrl = self::QR_API_BASE_URL . '?' . http_build_query($qrParams);

            Log::info('Generating QR code from API', [
                'url' => $url,
                'api_url' => $qrUrl
            ]);

            $response = Http::timeout(self::QR_API_TIMEOUT)->get($qrUrl);

            if ($response->successful()) {
                Log::info('QR code generated successfully from API');
                return $response->body();
            } else {
                Log::error('QR API request failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('QR code API generation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Place QR code on image using image data from API
     */
    private function placeQrCodeOnImage($img, string $qrImageData): void
    {
        try {
            // Create QR image from API response data
            $qrImg = $this->manager->read($qrImageData);

            // Resize if needed (API should return correct size, but just in case)
            if ($qrImg->width() !== self::QR_SIZE || $qrImg->height() !== self::QR_SIZE) {
                $qrImg->resize(self::QR_SIZE, self::QR_SIZE);
            }

            $width = $img->width() - 423;
            $qrX = ($width - self::QR_SIZE) / 2;
            $qrY = self::QR_Y_POSITION;

            // Place QR code
            $img->place($qrImg, 'top-left', (int)$qrX, (int)$qrY);

            Log::info('QR code placed successfully on name tag');

        } catch (\Exception $e) {
            Log::error('Failed to place QR code on image: ' . $e->getMessage());
        }
    }

    /**
     * Save name tag and return URL
     */
    private function saveNameTag($img, Visitor $visitor, string $prefix, string $directory): string
    {
        $filename = $prefix . $visitor->id . '_' . time() . '.png';
        $fullDirectory = public_path($directory);

        $this->ensureDirectoryExists($fullDirectory);

        $fullPath = $fullDirectory . '/' . $filename;
        $img->save($fullPath, 80, 'png');

        if (file_exists($fullPath)) {
            chmod($fullPath, 0644);
            Log::info('Name tag saved successfully', [
                'path' => $fullPath,
                'file_size' => filesize($fullPath)
            ]);

            return asset($directory . '/' . $filename);
        }

        Log::error('Name tag file was not created: ' . $fullPath);
        return $this->getPlaceholderUrl();
    }

    /**
     * Ensure directory exists
     */
    private function ensureDirectoryExists(string $directory): void
    {
        if (!File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
            Log::info('Created directory: ' . $directory);
        }
    }

    /**
     * Handle errors consistently
     */
    private function handleError(string $operation, \Exception $e, int $visitorId): string
    {
        Log::error("Error generating {$operation}: " . $e->getMessage(), [
            'visitor_id' => $visitorId,
            'exception' => $e
        ]);
        return $this->getPlaceholderUrl();
    }

    /**
     * Get placeholder URL
     */
    private function getPlaceholderUrl(): string
    {
        return asset(self::PLACEHOLDER);
    }

    /**
     * Apply rounded corners to image
     */
    private function applyRoundedCorners($image, int $radius = 80): void
    {
        // Imagick implementation
        if ($image->driver() instanceof ImagickDriver) {
            try {
                $core = $image->core()->native();
                $width = $image->width();
                $height = $image->height();

                $mask = new \Imagick();
                $mask->newImage($width, $height, new \ImagickPixel('transparent'));
                $mask->setImageFormat('png');

                $draw = new \ImagickDraw();
                $draw->setFillColor(new \ImagickPixel('white'));
                $draw->roundRectangle(0, 0, $width, $height, $radius, $radius);
                $mask->drawImage($draw);

                if (method_exists($core, 'setImageAlphaChannel') && defined('Imagick::ALPHACHANNEL_SET')) {
                    $core->setImageAlphaChannel(\Imagick::ALPHACHANNEL_SET);
                } elseif (method_exists($core, 'setImageMatte')) {
                    $core->setImageMatte(true);
                }

                if (method_exists($core, 'compositeImage')) {
                    $core->compositeImage($mask, \Imagick::COMPOSITE_DSTIN, 0, 0);
                }

                $mask->clear();
                $mask->destroy();

            } catch (\Throwable $e) {
                Log::warning('Imagick rounding failed: ' . $e->getMessage());
            }
            return;
        }

        // GD implementation
        if ($image->driver() instanceof GdDriver) {
            try {
                $w = $image->width();
                $h = $image->height();

                $gdCore = $image->core();
                $gdPhoto = $gdCore->native();

                imagealphablending($gdPhoto, false);
                imagesavealpha($gdPhoto, true);

                $mask = imagecreatetruecolor($w, $h);
                imagesavealpha($mask, true);
                $transparent = imagecolorallocatealpha($mask, 0, 0, 0, 127);
                imagefill($mask, 0, 0, $transparent);

                $opaque = imagecolorallocatealpha($mask, 255, 255, 255, 0);

                imagefilledrectangle($mask, $radius, 0, $w - $radius, $h, $opaque);
                imagefilledrectangle($mask, 0, $radius, $w, $h - $radius, $opaque);
                imagefilledellipse($mask, $radius, $radius, $radius * 2, $radius * 2, $opaque);
                imagefilledellipse($mask, $w - $radius, $radius, $radius * 2, $radius * 2, $opaque);
                imagefilledellipse($mask, $radius, $h - $radius, $radius * 2, $radius * 2, $opaque);
                imagefilledellipse($mask, $w - $radius, $h - $radius, $radius * 2, $radius * 2, $opaque);

                for ($y = 0; $y < $h; $y++) {
                    for ($x = 0; $x < $w; $x++) {
                        $maskPixel = imagecolorsforindex($mask, imagecolorat($mask, $x, $y));
                        if ($maskPixel['alpha'] == 127) {
                            $photoPixel = imagecolorsforindex($gdPhoto, imagecolorat($gdPhoto, $x, $y));
                            $newColor = imagecolorallocatealpha($gdPhoto, $photoPixel['red'], $photoPixel['green'], $photoPixel['blue'], 127);
                            imagesetpixel($gdPhoto, $x, $y, $newColor);
                        }
                    }
                }

                imagedestroy($mask);
            } catch (\Throwable $e) {
                Log::warning('GD rounding failed: ' . $e->getMessage());
            }
        }
    }
}
