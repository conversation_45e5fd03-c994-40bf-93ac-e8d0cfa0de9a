<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\VisitorTicket;
use App\Models\Visitor;
use App\Services\Payments\StripePaymentService;
use App\Services\TicketService;
use App\Services\NotificationService;
use App\Services\TagNameTagGenerator;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads; // Added for file uploads

class PaymentProcess extends Component
{
    use WithFileUploads; // Added for file uploads

    public ?VisitorTicket $ticket = null;
    public ?Visitor $visitor = null;
    public $stripe_session_id;
    public bool $showNameTag = false;
    public $nameTagUrl = null;
    public $newPhoto = null; // Added for image cropper compatibility
    public $profilePhoto = null; // Legacy alias to prevent missing-property errors
    // Added to support WhatsApp dropdown in name-tag partial
    public $whatsappCountryCode = 'EG';

    // Services
    protected $stripeService;
    protected $ticketService;
    protected $notificationService;
    protected $nameTagGenerator;

    public function boot()
    {
        $this->stripeService = new StripePaymentService();
        $this->ticketService = new TicketService();
        $this->notificationService = new NotificationService();
        $this->nameTagGenerator = new TagNameTagGenerator();
    }

    public function mount($ticketId = null)
    {
        if (auth('visitor')->check()) {
            $this->visitor = auth('visitor')->user();

            if ($ticketId) {
                $this->ticket = VisitorTicket::where('id', $ticketId)
                    ->where('visitor_id', $this->visitor->id)
                    ->firstOrFail();
            } else {
                $this->ticket = $this->visitor->visitorTickets()
                    ->where('payment_status', 0)
                    ->orderBy('created_at', 'desc')
                    ->first();
            }
            // Handle payment callbacks
            $this->handleCallbackParameters();
        } else {
            session()->flash('error', 'You must be logged in to manage your ticket.');
        }
    }

    /**
     * Handle callback parameters from payment gateway redirects
     */
    protected function handleCallbackParameters()
    {
        if (request()->has('ticketId_callback') && $this->ticket) {
            Log::debug("Found ticket ID in request: " . request('ticketId_callback'));

            if (Session::has('stripe_payment_checkout_session_id')) {
                Log::debug("Stripe checkout session found in Laravel session");
                $this->stripe_session_id = Session::get('stripe_payment_checkout_session_id');
                return $this->handlePaymentSuccess();
            }

            if (request()->has('payment_cancelled')) {
                Log::debug("Found payment_cancelled flag");
                return $this->handlePaymentCancel();
            }
        }
    }

    /**
     * Process ticket purchase - either free or initiate payment
     */
    public function purchaseTicket()
    {
        if (!$this->validatePurchaseRequest()) {
            return;
        }

        // Process free ticket
        if ($this->ticket->ticket->type === 'free' && $this->ticket->ticket_price == 0) {
            return $this->processFreeTicket();
        }

        // Process paid ticket - initiate payment
        return $this->initiatePaidTicketPayment();
    }

    /**
     * Validate basic requirements for ticket purchase
     */
    protected function validatePurchaseRequest()
    {
        if (!auth('visitor')->check()) {
            session()->flash('error', 'Authentication required.');
            return redirect()->route('visitor.login');
        }

        $this->visitor = auth('visitor')->user();

        if (!$this->ticket) {
            session()->flash('error', 'No ticket selected for payment.');
            return redirect()->route('visitor.ticket');
        }

        return true;
    }

    /**
     * Process a free ticket without payment
     */
    protected function processFreeTicket()
    {
        $success = $this->ticketService->processFreeTicket($this->visitor, $this->ticket);

        if ($success) {
            $this->notificationService->sendTicketConfirmation($this->visitor, $this->ticket);
            session()->flash('success', 'Your free ticket is confirmed! Share with your peers on LinkedIn.');
            $this->generateNameTag();
        } else {
            session()->flash('error', 'Failed to process your free ticket. Please try again.');
        }

        return redirect()->route('visitor.ticket');
    }

    /**
     * Initiate payment for a paid ticket
     */
    protected function initiatePaidTicketPayment()
    {
        try {
            $successUrl = route('visitor.ticket.payment.success', [
                'ticketId_callback' => $this->ticket->id,
                'source'            => 'livewire',
            ]);

            $cancelUrl = route('visitor.ticket.payment.cancel', [
                'ticketId_callback'  => $this->ticket->id,
                'payment_cancelled' => true,
                'source'            => 'livewire',
            ]);

            $paymentData = [
                'customer_email' => $this->visitor->email,
                'product_name' => 'AFP Future of Finance | Cairo 2025 - ' . $this->ticket->ticket->name,
                'description' => 'This ticket grants you access to the AFP Future Of Finance | Cairo on December 7, 2025. The event will be held at the Royal Maxim Palace Kempinski  Cairo Hotel in Cairo, Egypt. The ticket includes access to all keynote speeches, panel discussions, and access to one workshop.'
            ];

            $result = $this->stripeService->createCheckoutSession(
                $paymentData,
                $this->ticket->ticket_price,
                $successUrl,
                $cancelUrl
            );

            if (!$result['success']) {
                session()->flash('error', 'Payment initiation failed: ' . ($result['error'] ?? 'Unknown error'));
                return redirect()->route('visitor.ticket');
            }

            Session::put('stripe_payment_checkout_session_id', $result['id']);
            return redirect($result['url']);

        } catch (\Exception $e) {
            Log::error('Payment initiation error: ' . $e->getMessage());
            session()->flash('error', 'An unexpected error occurred. Please try again.');
            return redirect()->route('visitor.ticket');
        }
    }

    /**
     * Handle successful payment callback
     */
    public function handlePaymentSuccess()
    {
        if (!$this->verifyPaymentContext()) {
            return redirect()->route('visitor.ticket');
        }

        try {
            $paymentResult = $this->stripeService->verifyPayment($this->stripe_session_id);
            // Clear the session ID from Laravel session immediately after verification attempt
            Session::forget('stripe_payment_checkout_session_id');

            if (!$paymentResult['success']) {
                session()->flash('error', 'Could not retrieve payment details: ' . $paymentResult['error']);
                return redirect()->route('visitor.ticket');
            }

            if (!$paymentResult['is_paid']) {
                session()->flash('error', 'Payment was not successful. Status: ' . $paymentResult['payment_status']);
                return redirect()->route('visitor.ticket');
            }

            // Idempotency: If ticket already marked as paid in DB
            if ($this->ticket->payment_status == 1) {
                session()->flash('success', 'Payment confirmed. Your ticket was already processed.');
            }

            // Process the paid ticket (update DB, generate QR, etc.)
            $ticketProcessingSuccess = $this->ticketService->processPaidTicket(
                $this->visitor,
                $this->ticket,
                $paymentResult['payment_intent']
            );

            if ($ticketProcessingSuccess) {
                $this->notificationService->sendTicketConfirmation($this->visitor, $this->ticket);
                session()->flash('success', 'Payment successful! Your ticket is confirmed. View your name tag below.');
                $this->showNameTag = true; // Show name tag

                // Generate name tag image
                $this->generateNameTag();

                // Do not redirect, allow component to re-render with name tag
            } else {
                session()->flash('error', 'Payment was received, but ticket processing failed. Please contact support.');
                return redirect()->route('visitor.ticket'); // Redirect on processing failure
            }

        } catch (\Exception $e) {
            Log::error('Payment verification error: ' . $e->getMessage(), ['exception' => $e]);
            session()->flash('error', 'An error occurred while processing your payment. Please try again or contact support.');
            return redirect()->route('visitor.ticket');
        }
    }

    /**
     * Generate a name tag image for the visitor
     */
    protected function generateNameTag()
    {
        try {
            // Reload the ticket to ensure we have the latest data
            $this->ticket->refresh();

            // Generate both name tags - old one for display in blade and new one stored separately
            $oldNameTagUrl = $this->nameTagGenerator->generateNameTag($this->visitor, $this->ticket);
            $newNameTagUrl = $this->nameTagGenerator->generateNewNameTag($this->visitor, $this->ticket);

            // Update the visitor with both name tag URLs
            $this->visitor->update([
                'name_tag' => $oldNameTagUrl,
                'event_name_tag' => $newNameTagUrl,
                'name_tag_status' => 'generated',
                'name_tag_date' => now()
            ]);

            // Keep the nameTagUrl property set to the old style (for blade display)
            $this->nameTagUrl = $oldNameTagUrl;

            Log::info('Both name tags generated successfully for visitor: ' . $this->visitor->id . ', ticket: ' . $this->ticket->id);
        } catch (\Exception $e) {
            dd($e->getMessage());
            Log::error('Name tag generation error: ' . $e->getMessage(), ['exception' => $e]);
        }
    }


    /**
     * Verify that we have all necessary context for payment processing
     */
    protected function verifyPaymentContext()
    {
        if (!auth('visitor')->check()) {
            session()->flash('error', 'Authentication required.');
            return redirect()->route('visitor.login');
        }

        $this->visitor = auth('visitor')->user();

        if (!$this->stripe_session_id || !$this->ticket) {
            session()->flash('error', 'Invalid payment session details.');
            return false;
        }

        return true;
    }

    /**
     * Handle payment cancellation
     */
    public function handlePaymentCancel()
    {
        Session::forget('stripe_payment_checkout_session_id');
        session()->flash('error', 'Payment was cancelled.');
        return redirect()->route('visitor.ticket');
    }

    /**
     * Handle photo upload from the visitor name tag cropper
     */
    public function updatedNewPhoto()
    {
        try {
            if ($this->newPhoto) {
                $this->validate([
                    'newPhoto' => 'image|max:1024', // 1MB max
                ]);

                // Generate a unique filename based on visitor ID and current timestamp
                $filename = $this->visitor->id . '_' . time() . '.' . $this->newPhoto->getClientOriginalExtension();

                // Make sure the directory exists
                $storage = Storage::disk('public');
                if (!$storage->exists('visitor_images')) {
                    $storage->makeDirectory('visitor_images');
                }

                // Store the file - note we're not using a nested 'visitors' folder
                $path = $this->newPhoto->storeAs('visitor_images', $filename, 'public');

                // Make sure the file is readable
                $fullPath = storage_path('app/public/' . $path);
                if (file_exists($fullPath)) {
                    chmod($fullPath, 0644); // Ensure file is readable
                }

                // Log success
                Log::info('Visitor photo stored successfully at: ' . $path);

                // Update visitor record with just the filename
                $this->visitor->image = $filename;
                $this->visitor->save();

                // Refresh the model so component re-render uses latest data
                $this->visitor->refresh();

                // Notify frontend to ensure spinner stops if still visible
                $imageUrl = asset('storage/visitor_images/' . $filename);
                if (method_exists($this, 'dispatchBrowserEvent')) {
                    // Livewire v2 style
                    $this->dispatchBrowserEvent('visitor-photo-updated', ['imageUrl' => $imageUrl]);
                } elseif (method_exists($this, 'dispatch')) {
                    // Livewire v3 style – browser dispatch is default
                    $this->dispatch('visitor-photo-updated', imageUrl: $imageUrl);
                }

                // Dispatch event for SweetAlert instead of using session flash
                $this->dispatch('swal:success', [
                    'title' => 'Success!',
                    'text' => 'Your photo has been uploaded successfully.'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error uploading visitor photo: ' . $e->getMessage());
            $this->dispatch('swal:error', [
                'title' => 'Error!',
                'text' => 'Failed to upload photo: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Fallback handler for legacy "profilePhoto" uploads initiated by old JS.
     * Simply forward to the newPhoto handler.
     */
    public function updatedProfilePhoto()
    {
        $this->newPhoto = $this->profilePhoto;
        $this->updatedNewPhoto();
    }

    // https://www.linkedin.com/feed/

    public function render()
    {
        // Double-check for payment callbacks in render if not already handled or showing name tag
        if (!$this->showNameTag && // If name tag isn't already set to show
            !$this->stripe_session_id && // And we don't have a stripe_session_id in the component state yet
            Session::has('stripe_payment_checkout_session_id') && // But a checkout session id is stored in Laravel session
            $this->ticket && // And we have a ticket context
            request()->has('ticketId_callback') && // And the ticketId_callback is present
            request('ticketId_callback') == $this->ticket->id // And it matches the current ticket ID
        ) {
            Log::debug("Render method detected payment callback for ticket: " . $this->ticket->id . " using session from Laravel session");
            $this->stripe_session_id = Session::get('stripe_payment_checkout_session_id');
            $this->handlePaymentSuccess(); // This will set showNameTag if successful and not redirect
        }

        return view('livewire.visitor_ticket');
    }
}
