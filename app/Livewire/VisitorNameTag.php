<?php

namespace App\Livewire;

use App\Models\Agenda;
use App\Models\Visitor;
use App\Models\VisitorTicket;
use App\Traits\WithCountryHandling;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class VisitorNameTag extends Component
{
    use WithFileUploads, WithCountryHandling;

    public Visitor $visitor;
    public ?VisitorTicket $ticket = null;

    // Form fields
    public $firstName, $lastName, $email, $jobTitle1, $jobTitle2, $companyName, $linkedinProfile, $whatsappNumber, $workshops, $selectedWorkshops;
    public $newPhoto, $existingPhoto = null, $ticketType = 'N/A';
    public $countryCode; // whatsappCountryCode is already defined in WithCountryHandling trait

    protected function rules()
    {
        return [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:visitors,email,' . $this->visitor->id,
            'jobTitle1' => 'nullable|string|max:255',
            'jobTitle2' => 'nullable|string|max:255',
            'companyName' => 'nullable|string|max:255',
            'linkedinProfile' => 'nullable|url|max:255',
            'whatsappNumber' => 'nullable|string|max:20',
            'workshops' => 'nullable',
            'selectedWorkshops' => 'nullable|exists:agendas,id', // Validate single selected workshop ID
            // Photo is optional; validate only if provided
            'newPhoto' => 'nullable|image|max:1024',
        ];
    }

    public function mount(Visitor $visitor, VisitorTicket $ticket = null)
    {
        $this->visitor = $visitor;
        $this->ticket = $ticket;

        // Pre-fill form fields
        $this->firstName = $visitor->first_name;
        $this->lastName = $visitor->second_name;
        $this->email = $visitor->email;
        $this->jobTitle1 = $visitor->title1;
        $this->jobTitle2 = $visitor->title2;
        $this->companyName = $visitor->company;
        $this->linkedinProfile = $visitor->linkedin;
        $this->whatsappNumber = $visitor->whatsapp_number;
        $this->existingPhoto = $visitor->image;
        $this->ticketType = $this->ticket?->ticket?->type ?? 'N/A';

        // Initialize country handling
        $this->loadCountries();
        $this->countryCode = $visitor->country_code ?? 'EG';
        // Determine WhatsApp country code
        $this->whatsappCountryCode = $visitor->whatsapp_country_code;
        if (empty($this->whatsappCountryCode) && !empty($this->whatsappNumber)) {
            // Try to extract calling digits from stored number e.g. +20123456
            if (preg_match('/^\+?(\d{1,4})/', $this->whatsappNumber, $m)) {
                $possibleCountry = \App\Services\CountryService::getCountryByCallingCode($m[1]);
                if ($possibleCountry) {
                    $this->whatsappCountryCode = strtoupper($possibleCountry['code']);
                }
            }
        }
        if (empty($this->whatsappCountryCode)) {
            $this->whatsappCountryCode = $this->countryCode; // fallback
        }
        // Strip international prefix from stored WhatsApp number for nicer display
        if (!empty($this->whatsappNumber)) {
            $digits = \App\Services\CountryService::getCallingCode($this->whatsappCountryCode);
            $pattern = '/^\+?' . preg_quote($digits, '/') . '/';
            $this->whatsappNumber = preg_replace($pattern, '', ltrim($this->whatsappNumber));
        }
        $this->updateCallingCode();

        // Initialize workshops
        $this->workshops =Agenda::orderBy('start_from','asc')->get();

        // Get the ID of the first associated agenda for single select
        $this->selectedWorkshops = $visitor->agendas()->first()?->id;
    }

    public function update(): void
    {
        try {
            $this->visitor->update([
                'first_name' => $this->firstName,
                'second_name' => $this->lastName,
                'email' => $this->email,
                'title1' => $this->jobTitle1,
                'title2' => $this->jobTitle2,
                'company' => $this->companyName,
                'linkedin' => $this->linkedinProfile,
                'whatsapp_number' => $this->whatsappNumber,
                'country_code' => $this->countryCode,
                'whatsapp_country_code' => $this->whatsappCountryCode,
            ]);
            if ($this->newPhoto) {
                if ($this->visitor->image && Storage::disk('public')->exists('visitor_images/' . $this->visitor->image)) {
                    Storage::disk('public')->delete('visitor_images/' . $this->visitor->image);
                }

                // Generate consistent filename as used elsewhere
                $filename = 'visitor_' . $this->visitor->id . '_' . time() . '.' . $this->newPhoto->getClientOriginalExtension();

                $this->newPhoto->storeAs('visitor_images', $filename, 'public');

                $this->visitor->update(['image' => $filename]);
            }
            // Sync the selected workshop (since it's required, we expect an ID)
            if ($this->selectedWorkshops) {
                $this->visitor->agendas()->sync([$this->selectedWorkshops]);
            } else {
                $this->visitor->agendas()->detach();
            }

            // Regenerate name tag after updating visitor data
            if ($this->ticket) {
                $nameTagGenerator = new \App\Services\TagNameTagGenerator();

                // Generate both name tags - old one for display in blade and new one stored separately
                $oldNameTagUrl = $nameTagGenerator->generateNameTag($this->visitor->fresh(), $this->ticket);
                $newNameTagUrl = $nameTagGenerator->generateNewNameTag($this->visitor->fresh(), $this->ticket);

                // Update the visitor's name_tag field with the old URL (for blade display)
                $this->visitor->update([
                    'name_tag' => $oldNameTagUrl,
                    'event_name_tag' => $newNameTagUrl
                ]);
            }

            $this->mount($this->visitor->fresh(), $this->ticket);

            // Dispatch event for SweetAlert instead of using session flash
            $this->dispatch('swal:success', [
                'title' => 'Success!',
                'text' => 'Your details have been updated successfully.'
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating visitor details: ' . $e->getMessage());

            // Dispatch event for SweetAlert instead of using session flash
            $this->dispatch('swal:error', [
                'title' => 'Error!',
                'text' => 'Failed to update your details. Please try again.'
            ]);
        }
    }

    public function render()
    {
        // Always use latest visitor data (ensures updated image shows without full reload)
        if ($this->visitor->exists) {
            $this->visitor->refresh();
        }
        return view('livewire.visitor-name-tag');
    }
}
