<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;

class VisitorTicket extends Component
{
    use WithFileUploads;
    
    public $visitor;
    public $showVerificationPopup = false;
    public $verificationMessage = '';
    public $newPhoto = null;
    
    // Form fields from visitor-name-tag
    public $firstName, $lastName, $email, $jobTitle1, $jobTitle2, $companyName, $linkedinProfile, $whatsappNumber, $countryCode;

    public function mount()
    {
        $this->visitor = Auth::guard('visitor')->user();

        // Check if there's a success message in the session
        if (session()->has('success')) {
            $this->showVerificationPopup = true;
            $this->verificationMessage = session('success');
        }
        
        // Initialize form fields with visitor data
        if ($this->visitor) {
            $this->firstName = $this->visitor->first_name;
            $this->lastName = $this->visitor->second_name;
            $this->email = $this->visitor->email;
            $this->jobTitle1 = $this->visitor->title1;
            $this->jobTitle2 = $this->visitor->title2;
            $this->companyName = $this->visitor->company;
            $this->linkedinProfile = $this->visitor->linkedin;
            $this->whatsappNumber = $this->visitor->whatsapp_number;
            $this->countryCode = $this->visitor->country_code;
        }
    }

    public function closePopup()
    {
        $this->showVerificationPopup = false;
    }

    /**
     * Remove the uploaded photo
     */
    public function removePhoto()
    {
        $this->newPhoto = null;
    }
    
    /**
     * Handle photo upload validation
     */
    public function updatedNewPhoto()
    {
        $this->validate([
            'newPhoto' => 'image|max:1024', // 1MB Max
        ]);
    }

    /**
     * Define validation rules
     */
    protected function rules()
    {
        return [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:visitors,email,' . $this->visitor->id,
            'jobTitle1' => 'nullable|string|max:255',
            'jobTitle2' => 'nullable|string|max:255',
            'companyName' => 'nullable|string|max:255',
            'linkedinProfile' => 'nullable|url|max:255',
            'whatsappNumber' => 'nullable|string|max:20',
            'newPhoto' => 'nullable|image|max:1024', // 1MB Max
        ];
    }

    /**
     * Update visitor information and photo
     */
    public function update()
    {
        $this->validate($this->rules());

        try {
            // Update visitor data
            $this->visitor->update([
                'first_name' => $this->firstName,
                'second_name' => $this->lastName,
                'email' => $this->email,
                'title1' => $this->jobTitle1,
                'title2' => $this->jobTitle2,
                'company' => $this->companyName,
                'linkedin' => $this->linkedinProfile,
                'whatsapp_number' => $this->whatsappNumber,
                'country_code' => $this->countryCode,
            ]);
            
            // Handle photo upload
            if ($this->newPhoto) {
                // Delete old photo if exists
                if ($this->visitor->image && \Illuminate\Support\Facades\Storage::disk('public')->exists('visitor_images/' . $this->visitor->image)) {
                    \Illuminate\Support\Facades\Storage::disk('public')->delete('visitor_images/' . $this->visitor->image);
                } else if ($this->visitor->image && \Illuminate\Support\Facades\Storage::disk('public')->exists($this->visitor->image)) {
                    // Legacy path support
                    \Illuminate\Support\Facades\Storage::disk('public')->delete($this->visitor->image);
                }
                
                // Generate consistent filename as used elsewhere
                $filename = 'visitor_' . $this->visitor->id . '_' . time() . '.' . $this->newPhoto->getClientOriginalExtension();
                
                // Store new photo in visitor_images directory (for consistency)
                $fullPath = $this->newPhoto->storeAs('visitor_images', $filename, 'public');
                
                // Store only the filename in the database
                $this->visitor->update(['image' => $filename]);
            }
            
            // Generate name tag if ticket exists
            $anyPaidTicket = $this->visitor->tickets()->where('status', 1)->first();
            if ($anyPaidTicket) {
                $nameTagGenerator = new \App\Services\TagNameTagGenerator();
                $nameTagUrl = $nameTagGenerator->generateNameTag($this->visitor->fresh(), $anyPaidTicket);
                $this->visitor->update(['name_tag' => $nameTagUrl]);
            }
            
            // Refresh visitor data
            $this->visitor = $this->visitor->fresh();
            
            // Show success message
            session()->flash('message', 'Your details have been updated successfully.');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error updating visitor details: ' . $e->getMessage());
            session()->flash('error', 'Failed to update your details. Please try again.');
        }
    }

    public function render()
    {
        return view('livewire.visitor_ticket');
    }
}
