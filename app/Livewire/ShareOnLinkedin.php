<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Visitor;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class ShareOnLinkedin extends Component
{
    public Visitor $visitor;

    private string $authUrl = 'https://www.linkedin.com/oauth/v2/authorization';
    private string $tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    private string $profileUrl = 'https://api.linkedin.com/v2/userinfo';
    private string $uploadUrl = 'https://api.linkedin.com/v2/assets?action=registerUpload';
    private string $postUrl = 'https://api.linkedin.com/v2/ugcPosts';

    public function mount(Visitor $visitor): void
    {
        $this->visitor = $visitor;
        if (request()->filled('code')) {
            $this->handleCallback(request('code'));
        } elseif (request()->filled('error')) {
            session()->flash('error', 'LinkedIn authorisation failed: ' . request('error_description'));
        }
    }

    /* ===================== PUBLIC ACTION ===================== */
    public function share()
    {
        Session::put('share_linkedin_visitor_id', $this->visitor->id);

        $params = [
            'response_type' => 'code',
            'client_id' => config('services.linkedin.client_id'),
            'redirect_uri' => config('services.linkedin.redirect'),
            'scope' => 'openid profile w_member_social',
            'state' => Str::random(32),
        ];

        return redirect()->away($this->authUrl . '?' . http_build_query($params));
    }

    /* ===================== CALLBACK HANDLER ===================== */
    private function handleCallback(string $code): void
    {
        $visitorId = Session::pull('share_linkedin_visitor_id');
        $visitor = Visitor::find($visitorId);
        if (!$visitor || !$visitor->name_tag) {
            session()->flash('error', 'Name tag not found for visitor.');
            return;
        }

        try {
            $accessToken = $this->exchangeCodeForToken($code);
            $profileId = $this->fetchLinkedInProfileId($accessToken);
            $assetUrn = $this->uploadImageToLinkedIn($accessToken, $profileId, $visitor->name_tag);
            $this->createImagePost($accessToken, $profileId, $assetUrn);

            $visitor->posted_at = now();
            $visitor->save();

            session()->flash('message', 'Your name tag was shared on LinkedIn!');
        } catch (\Throwable $e) {

            session()->flash('error', 'LinkedIn error: ' . $e->getMessage());
        }
    }

    /* ===================== LINKEDIN HELPERS ===================== */
    private function exchangeCodeForToken(string $code): string
    {
        $response = Http::asForm()->post($this->tokenUrl, [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'redirect_uri' => config('services.linkedin.redirect'),
            'client_id' => config('services.linkedin.client_id'),
            'client_secret' => config('services.linkedin.client_secret'),
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to obtain access token: ' . $response->body());
        }

        return $response->json('access_token');
    }

    private function fetchLinkedInProfileId(string $accessToken): string
    {
        $response = Http::withToken($accessToken)->get($this->profileUrl);
        if (!$response->successful()) {
            throw new \Exception('Failed fetching LinkedIn profile: ' . $response->body());
        }
        return $response->json('sub');
    }

    private function uploadImageToLinkedIn(string $accessToken, string $profileId, string $publicUrl): string
    {
        $imagePath = $this->localImagePath($publicUrl);

        // Register upload session
        $register = Http::withToken($accessToken)->post($this->uploadUrl, [
            'registerUploadRequest' => [
                'recipes' => ['urn:li:digitalmediaRecipe:feedshare-image'],
                'owner' => "urn:li:person:{$profileId}",
                'serviceRelationships' => [[
                    'relationshipType' => 'OWNER',
                    'identifier' => 'urn:li:userGeneratedContent',
                ]],
            ],
        ]);
        if (!$register->successful()) {
            throw new \Exception('LinkedIn register upload error: ' . $register->body());
        }

        $uploadInfo = $register->json('value');
        $assetUrn = $uploadInfo['asset'];
        $uploadUrl = $uploadInfo['uploadMechanism']['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest']['uploadUrl'];

        // Upload binary
        $mime = mime_content_type($imagePath) ?: 'image/png';
        $imageData = file_get_contents($imagePath);

        $upload = Http::withToken($accessToken)
            ->withHeaders(['Content-Type' => $mime])
            ->send('POST', $uploadUrl, ['body' => $imageData]);

        if (!$upload->successful()) {
            throw new \Exception('LinkedIn image upload failed: ' . $upload->body());
        }

        return $assetUrn;
    }

    private function createImagePost(string $accessToken, string $profileId, string $assetUrn): void
    {
        $text = "#I_am_attending_FOF Future of Finance MEA | Cairo 2025!
                This event will bring together financial leaders to explore the latest trends and shape the future of finance in our MEA region.
                Looking forward to a day filled with insightful discussions, innovative ideas, and valuable networking opportunities. ";

        $response = Http::withToken($accessToken)
            ->withHeaders(['X-Restli-Protocol-Version' => '2.0.0'])
            ->post($this->postUrl, [
                'author' => "urn:li:person:{$profileId}",
                'lifecycleState' => 'PUBLISHED',
                'specificContent' => [
                    'com.linkedin.ugc.ShareContent' => [
                        'shareCommentary' => ['text' => $text],
                        'shareMediaCategory' => 'IMAGE',
                        'media' => [[
                            'status' => 'READY',
                            'description' => ['text' => 'My event name tag'],
                            'media' => $assetUrn,
                            'title' => ['text' => 'Name Tag'],
                        ]],
                    ],
                ],
                'visibility' => ['com.linkedin.ugc.MemberNetworkVisibility' => 'PUBLIC'],
            ]);

        \Log::info('LinkedIn share response', $response->json());

        if (!$response->successful()) {
            throw new \Exception('Failed to create LinkedIn post: ' . $response->body());
        }
    }

    /* ===================== UTIL ===================== */
    private function localImagePath(string $publicUrl): string
    {
        // Extract filename from URL
        $filename = basename(parse_url($publicUrl, PHP_URL_PATH));

        // Since name tags are now saved in public/images/name_tags/
        $path = public_path('images/name_tags/' . $filename);

        if (!file_exists($path)) {
            // Log for debugging
            \Log::error('Name tag file not found', [
                'expected_path' => $path,
                'public_url' => $publicUrl,
                'filename' => $filename
            ]);
            throw new \Exception('Local image not found for upload: ' . $path);
        }

        return $path;
    }

    public function render()
    {
        return view('livewire.share-on-linkedin');
    }
}
